<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php?redirect=inbox.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Check if message ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('inbox.php');
}

$messageId = (int) $_GET['id'];

// Get message details
$sql = "SELECT m.*, p.title as property_title, u.username as sender_name, u.email as sender_email 
        FROM messages m 
        LEFT JOIN properties p ON m.property_id = p.id 
        LEFT JOIN users u ON m.sender_id = u.id 
        WHERE m.id = ? AND m.receiver_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $messageId, $userId);
$stmt->execute();
$result = $stmt->get_result();

// Check if message exists and belongs to the user
if ($result->num_rows !== 1) {
    redirect('inbox.php');
}

$message = $result->fetch_assoc();

// Mark message as read if not already
if (!$message['read_status']) {
    $sql = "UPDATE messages SET read_status = 1 WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $messageId);
    $stmt->execute();
}
?>

<!-- View Message Page -->
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">View Message</h1>
                <div>
                    <a href="inbox.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Inbox
                    </a>
                </div>
            </div>
            
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><?php echo $message['subject']; ?></h5>
                        <div>
                            <span class="badge bg-primary"><?php echo date('M d, Y H:i', strtotime($message['created_at'])); ?></span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4 border-bottom pb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>From:</strong> <?php echo $message['sender_name']; ?> (<?php echo $message['sender_email']; ?>)</p>
                                
                                <?php if ($message['property_id']): ?>
                                    <p class="mb-1">
                                        <strong>Property:</strong>
                                        <a href="property.php?id=<?php echo $message['property_id']; ?>" class="text-decoration-none">
                                            <?php echo $message['property_title']; ?>
                                        </a>
                                    </p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <p class="mb-1"><strong>Date:</strong> <?php echo date('F d, Y H:i', strtotime($message['created_at'])); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="message-content mb-4">
                        <?php echo nl2br($message['message']); ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="inbox.php?delete=<?php echo $message['id']; ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this message?');">
                                <i class="fas fa-trash me-2"></i> Delete
                            </a>
                        </div>
                        <div>
                            <a href="reply_message.php?id=<?php echo $message['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-reply me-2"></i> Reply
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?> 