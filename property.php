<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Do all redirects and header() calls BEFORE including header.php
if (!isLoggedIn()) {
    redirect('login.php');
}

// Check if property ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php');
}

$propertyId = (int) $_GET['id'];

// Get property details
$property = getPropertyById($propertyId);

// If property not found, redirect to home
if (!$property) {
    redirect('index.php');
}

// All redirects done, now safe to include header
require_once 'includes/header.php';

// Get property images
$propertyImages = getPropertyImages($propertyId);

// Format price
$formattedPrice = formatPrice($property['price'], $property['listing_type']);

// Process contact form if submitted
$contactSuccess = false;
$contactError = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_submit'])) {
    // Get form data
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = isset($_POST['phone']) ? sanitize($_POST['phone']) : '';
    $message = sanitize($_POST['message']);
    $sender_id = isLoggedIn() ? $_SESSION['user_id'] : 0;  // 0 for guests
    $receiver_id = $property['seller_id'];
    $subject = "Inquiry about property: " . $property['title'];
    
    // Insert message into database
    $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iiiss", $sender_id, $receiver_id, $propertyId, $subject, $message);
    
    if ($stmt->execute()) {
        $contactSuccess = true;
    } else {
        $contactError = "Failed to send message. Please try again.";
    }
}

function tableExists($conn, $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '" . $conn->real_escape_string($table) . "'");
        return $result && $result->num_rows > 0;
    } catch (Exception $e) {
        return false;
    }
}

$isSaved = false;
if (isLoggedIn() && $_SESSION['user_role'] === 'buyer') {
    try {
        if (tableExists($conn, 'saved_properties')) {
            $sql = "SELECT id FROM saved_properties WHERE user_id = ? AND property_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ii', $_SESSION['user_id'], $property['id']);
            $stmt->execute();
            $stmt->store_result();
            $isSaved = $stmt->num_rows > 0;
        }
    } catch (mysqli_sql_exception $e) {
        // Optionally log error
        $isSaved = false;
        echo '<div class="alert alert-danger">Save Property feature is temporarily unavailable. Please try again later.</div>';
    }
}

if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $conn->query("DELETE FROM messages WHERE sender_id = $id OR receiver_id = $id");
    $conn->query("DELETE FROM payments WHERE seller_id = $id OR buyer_id = $id");
    $conn->query("DELETE FROM ratings WHERE buyer_id = $id OR seller_id = $id");
    $conn->query("DELETE FROM users WHERE id = $id");
    echo '<div class="alert alert-success">User deleted.</div>';
}
?>

<!-- Property Details Page -->
<div class="container my-4">
    <?php if ($contactSuccess): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>Message Sent!</strong> Your inquiry has been sent to the seller. They will contact you soon.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    <?php if (!empty($contactError)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Message Error:</strong> <?php echo $contactError; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="property-header mb-4">
        <h1 class="h2"><?php echo $property['title']; ?></h1>
        <p class="text-muted">
            <i class="fas fa-map-marker-alt"></i> 
            <?php echo !empty($property['local_area'] ?? '') ? $property['local_area'] . ', ' . $property['location'] : $property['location']; ?>
        </p>
    </div>
    
    <!-- Main Property Content - Two Column Layout -->
    <div class="row property-main">
        <!-- Left Column - Property Images -->
        <div class="col-lg-7 mb-4">
            <?php if (count($propertyImages) > 1): ?>
                <!-- Image Slider for multiple images -->
                <div id="propertyImageCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-indicators">
                        <?php foreach ($propertyImages as $index => $image): ?>
                            <button type="button" data-bs-target="#propertyImageCarousel" data-bs-slide-to="<?php echo $index; ?>" <?php echo $index === 0 ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $index + 1; ?>"></button>
                        <?php endforeach; ?>
                    </div>
                    <div class="carousel-inner rounded">
                        <?php foreach ($propertyImages as $index => $image): ?>
                            <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                                <img src="<?php echo $image['image_path']; ?>" class="d-block w-100" alt="Property Image" style="height: 450px; object-fit: cover;">
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#propertyImageCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#propertyImageCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            <?php else: ?>
                <!-- Single Image Display -->
                <img src="<?php echo $propertyImages[0]['image_path']; ?>" class="img-fluid rounded" alt="<?php echo $property['title']; ?>" style="width: 100%; height: 450px; object-fit: cover;">
            <?php endif; ?>
            
            <!-- Thumbnail Gallery -->
            <?php if (count($propertyImages) > 1): ?>
                <div class="row mt-2">
                    <?php foreach ($propertyImages as $index => $image): ?>
                        <div class="col-3 col-md-2 mb-2">
                            <img src="<?php echo $image['image_path']; ?>" class="img-thumbnail" alt="Thumbnail" style="width: 100%; height: 60px; object-fit: cover; cursor: pointer;" onclick="$('#propertyImageCarousel').carousel(<?php echo $index; ?>)">
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Right Column - Property Details -->
        <div class="col-lg-5">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="mb-4">
                        <h3 class="text-primary h4 mb-3"><?php echo $formattedPrice; ?></h3>
                        
                        <div class="property-info mb-3">
                            <div class="row mb-2">
                                <div class="col-5">
                                    <span class="text-muted">Location:</span>
                                </div>
                                <div class="col-7">
                                    <?php echo !empty($property['local_area'] ?? '') ? $property['local_area'] . ', ' . $property['location'] : $property['location']; ?>
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <div class="col-5">
                                    <span class="text-muted">Property Type:</span>
                                </div>
                                <div class="col-7">
                                    <?php echo $property['property_type']; ?>
                                </div>
                            </div>
                            
                            <?php if ($property['bedrooms']): ?>
                            <div class="row mb-2">
                                <div class="col-5">
                                    <span class="text-muted">Bedrooms:</span>
                                </div>
                                <div class="col-7">
                                    <?php echo $property['bedrooms']; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($property['bathrooms']): ?>
                            <div class="row mb-2">
                                <div class="col-5">
                                    <span class="text-muted">Bathrooms:</span>
                                </div>
                                <div class="col-7">
                                    <?php echo $property['bathrooms']; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($property['area']): ?>
                            <div class="row mb-2">
                                <div class="col-5">
                                    <span class="text-muted">Area:</span>
                                </div>
                                <div class="col-7">
                                    <?php echo $property['area']; ?> m²
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="row mb-2">
                                <div class="col-5">
                                    <span class="text-muted">Property ID:</span>
                                </div>
                                <div class="col-7">
                                    <?php echo $property['id']; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <?php if ($paymentStatus['can_pay']): ?>
                                <a class="btn btn-success btn-lg" href="pay.php?property_id=<?php echo $property['id']; ?>">
                                    <i class="fas fa-shopping-cart me-2"></i> Pay Now - <?php echo $formattedPrice; ?>
                                </a>
                                <small class="text-muted text-center">
                                    <i class="fas fa-shield-alt me-1"></i> Secure payment
                                </small>
                            <?php elseif ($existingPayment): ?>
                                <div class="alert alert-success mb-2">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Sold</strong> to <?php echo htmlspecialchars($existingPayment['buyer_name']); ?>
                                </div>
                            <?php elseif (isLoggedIn() && $_SESSION['user_role'] === 'buyer'): ?>
                                <div class="alert alert-warning mb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?php echo $paymentStatus['message']; ?>
                                </div>
                            <?php endif; ?>
                            <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#contactSellerModal">
                                <i class="fas fa-envelope me-1"></i> Contact Seller
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- About This Property Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="h4 mb-3">About This Property</h3>
                    <p><?php echo nl2br($property['description']); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="h4 mb-3">Features</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-group list-group-flush">
                                <?php if ($property['bedrooms']): ?>
                                    <li class="list-group-item"><i class="fas fa-bed me-2 text-primary"></i> <?php echo $property['bedrooms']; ?> Bedrooms</li>
                                <?php endif; ?>
                                
                                <?php if ($property['bathrooms']): ?>
                                    <li class="list-group-item"><i class="fas fa-bath me-2 text-primary"></i> <?php echo $property['bathrooms']; ?> Bathrooms</li>
                                <?php endif; ?>
                                
                                <?php if ($property['area']): ?>
                                    <li class="list-group-item"><i class="fas fa-vector-square me-2 text-primary"></i> <?php echo $property['area']; ?> m² Area</li>
                                <?php endif; ?>
                                
                                <li class="list-group-item"><i class="fas fa-home me-2 text-primary"></i> Property Type: <?php echo $property['property_type']; ?></li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <ul class="list-group list-group-flush">
                                <?php if (isset($property['has_electricity']) && $property['has_electricity']): ?>
                                    <li class="list-group-item"><i class="fas fa-bolt me-2 text-warning"></i> Electricity</li>
                                <?php endif; ?>

                                <?php if (isset($property['has_water']) && $property['has_water']): ?>
                                    <li class="list-group-item"><i class="fas fa-tint me-2 text-info"></i> Water</li>
                                <?php endif; ?>

                                <?php if (isset($property['has_fence']) && $property['has_fence']): ?>
                                    <li class="list-group-item"><i class="fas fa-border-all me-2 text-secondary"></i> Fence</li>
                                <?php endif; ?>

                                <?php if (isset($property['has_gym']) && $property['has_gym']): ?>
                                    <li class="list-group-item"><i class="fas fa-dumbbell me-2 text-primary"></i> Gym</li>
                                <?php endif; ?>

                                <?php if (isset($property['has_air_condition']) && $property['has_air_condition']): ?>
                                    <li class="list-group-item"><i class="fas fa-snowflake me-2 text-info"></i> Air Conditioning</li>
                                <?php endif; ?>

                                <?php if (isset($property['has_security']) && $property['has_security']): ?>
                                    <li class="list-group-item"><i class="fas fa-shield-alt me-2 text-success"></i> Security</li>
                                <?php endif; ?>

                                <?php if (isset($property['has_pool']) && $property['has_pool']): ?>
                                    <li class="list-group-item"><i class="fas fa-swimming-pool me-2 text-primary"></i> Swimming Pool</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Features Section -->
    <?php
    // Define available features with their display information
    $availableFeatures = [
        'has_electricity' => ['icon' => 'fas fa-bolt text-warning', 'name' => 'Electricity'],
        'has_water' => ['icon' => 'fas fa-tint text-info', 'name' => 'Water'],
        'has_fence' => ['icon' => 'fas fa-border-all text-secondary', 'name' => 'Fence'],
        'has_gym' => ['icon' => 'fas fa-dumbbell text-primary', 'name' => 'Gym'],
        'has_air_condition' => ['icon' => 'fas fa-snowflake text-info', 'name' => 'Air Conditioning'],
        'has_security' => ['icon' => 'fas fa-shield-alt text-success', 'name' => 'Security'],
        'has_pool' => ['icon' => 'fas fa-swimming-pool text-primary', 'name' => 'Swimming Pool']
    ];

    // Collect enabled features
    $enabledFeatures = [];
    foreach ($availableFeatures as $featureKey => $featureInfo) {
        if (isset($property[$featureKey]) && $property[$featureKey] == 1) {
            $enabledFeatures[] = [
                'icon' => $featureInfo['icon'],
                'name' => $featureInfo['name']
            ];
        }
    }

    // Only show the section if there are enabled features
    if (!empty($enabledFeatures)):
    ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="h4 mb-3">Additional Features</h3>
                    <div class="row">
                        <?php
                        // Split features into two columns for better layout
                        $halfCount = ceil(count($enabledFeatures) / 2);
                        $leftFeatures = array_slice($enabledFeatures, 0, $halfCount);
                        $rightFeatures = array_slice($enabledFeatures, $halfCount);
                        ?>

                        <div class="col-md-6">
                            <ul class="list-group list-group-flush">
                                <?php foreach ($leftFeatures as $feature): ?>
                                    <li class="list-group-item">
                                        <i class="<?php echo $feature['icon']; ?> me-2"></i>
                                        <?php echo $feature['name']; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <?php if (!empty($rightFeatures)): ?>
                        <div class="col-md-6">
                            <ul class="list-group list-group-flush">
                                <?php foreach ($rightFeatures as $feature): ?>
                                    <li class="list-group-item">
                                        <i class="<?php echo $feature['icon']; ?> me-2"></i>
                                        <?php echo $feature['name']; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Map Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="h4 mb-3">Location</h3>
                    <?php if (!empty($property['lat']) && !empty($property['lng'])): ?>
                        <div id="property-map" style="height: 400px;" class="rounded"></div>
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                // Initialize the map
                                const lat = <?php echo $property['lat']; ?>;
                                const lng = <?php echo $property['lng']; ?>;
                                const propertyMap = L.map('property-map').setView([lat, lng], 15);
                                
                                // Add the OpenStreetMap tile layer
                                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                }).addTo(propertyMap);
                                
                                // Add marker for the property location
                                const marker = L.marker([lat, lng]).addTo(propertyMap);
                                
                                // Add popup with property info
                                marker.bindPopup(`
                                    <strong><?php echo $property['title']; ?></strong><br>
                                    <i class="fas fa-map-marker-alt"></i> <?php echo !empty($property['local_area'] ?? '') ? $property['local_area'] . ', ' . $property['location'] : $property['location']; ?><br>
                                    <strong><?php echo $formattedPrice; ?></strong>
                                `).openPopup();
                            });
                        </script>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-map-marker-alt me-2"></i> Exact location coordinates not available for this property.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Section -->
    <?php
    // Check payment status and user eligibility
    $paymentStatus = canUserPayForProperty($property);
    $existingPayment = getPropertyPaymentStatus($property['id']);
    ?>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <?php if ($existingPayment): ?>
                        <!-- Property Already Paid -->
                        <div class="alert alert-success">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle fa-2x me-3 text-success"></i>
                                <div>
                                    <h5 class="mb-1">Payment Complete</h5>
                                    <p class="mb-0">This property has been purchased by <strong><?php echo htmlspecialchars($existingPayment['buyer_name']); ?></strong> on <?php echo date('M j, Y', strtotime($existingPayment['date'])); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php elseif ($paymentStatus['can_pay']): ?>
                        <!-- Show Pay Now Button -->
                        <div class="text-center">
                            <h4 class="mb-3">
                                <i class="fas fa-credit-card me-2 text-primary"></i>
                                Purchase This Property
                            </h4>
                            <p class="text-muted mb-4">Secure payment powered by Flutterwave</p>
                            <div class="d-grid gap-2 d-md-block">
                                <a href="pay.php?property_id=<?php echo $property['id']; ?>"
                                   class="btn btn-success btn-lg px-5">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    Pay Now - <?php echo $formattedPrice; ?>
                                </a>
                            </div>
                            <small class="text-muted d-block mt-2">
                                <i class="fas fa-shield-alt me-1"></i>
                                Secure payment processing with buyer protection
                            </small>
                        </div>
                    <?php else: ?>
                        <!-- Show why payment is not available -->
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x me-3 text-info"></i>
                                <div>
                                    <h5 class="mb-1">Payment Not Available</h5>
                                    <p class="mb-0"><?php echo $paymentStatus['message']; ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Seller Section (Mobile) -->
    <div class="d-lg-none mb-4">
        <div class="card shadow-sm">
            <div class="card-body">
                <h3 class="h4 mb-3">Contact Seller</h3>
                <form action="property.php?id=<?php echo $propertyId; ?>" method="post" class="needs-validation" novalidate>
                    <input type="hidden" name="property_id" value="<?php echo $property['id']; ?>">
                    
                    <div class="mb-3">
                        <label for="mobile-name" class="form-label">Your Name</label>
                        <input type="text" class="form-control" id="mobile-name" name="name" 
                            value="<?php echo isLoggedIn() ? $_SESSION['username'] : ''; ?>" required>
                        <div class="invalid-feedback">
                            Please enter your name.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="mobile-email" class="form-label">Your Email</label>
                        <input type="email" class="form-control" id="mobile-email" name="email" 
                            value="<?php echo isLoggedIn() ? $_SESSION['email'] : ''; ?>" required>
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="mobile-phone" class="form-label">Your Phone</label>
                        <input type="tel" class="form-control" id="mobile-phone" name="phone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="mobile-message" class="form-label">Message</label>
                        <textarea class="form-control" id="mobile-message" name="message" rows="4" required>I am interested in this property (ID: <?php echo $property['id']; ?>). Please provide more information.</textarea>
                        <div class="invalid-feedback">
                            Please enter a message.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" name="contact_submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i> Send Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Contact Seller Modal -->
<div class="modal fade" id="contactSellerModal" tabindex="-1" aria-labelledby="contactSellerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactSellerModalLabel">Contact Seller</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="modalContactForm" action="property.php?id=<?php echo $propertyId; ?>" method="post" class="needs-validation" novalidate>
                    <input type="hidden" name="property_id" value="<?php echo $property['id']; ?>">
                    
                    <div class="mb-3">
                        <label for="modal-name" class="form-label">Your Name</label>
                        <input type="text" class="form-control" id="modal-name" name="name" 
                               value="<?php echo isLoggedIn() ? $_SESSION['username'] : ''; ?>" required>
                        <div class="invalid-feedback">
                            Please enter your name.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-email" class="form-label">Your Email</label>
                        <input type="email" class="form-control" id="modal-email" name="email" 
                               value="<?php echo isLoggedIn() ? $_SESSION['email'] : ''; ?>" required>
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-phone" class="form-label">Your Phone</label>
                        <input type="tel" class="form-control" id="modal-phone" name="phone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-message" class="form-label">Message</label>
                        <textarea class="form-control" id="modal-message" name="message" rows="4" required>I am interested in this property (ID: <?php echo $property['id']; ?>). Please provide more information.</textarea>
                        <div class="invalid-feedback">
                            Please enter a message.
                        </div>
                    </div>
                    <input type="hidden" name="contact_submit" value="1">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('modalContactForm').submit();">Send Message</button>
            </div>
        </div>
    </div>
</div>

<style>
.payment-summary {
    border-left: 4px solid var(--primary-color);
}

.card-details {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.payment-methods {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

.security-feature {
    padding: 1rem;
}

.security-feature i {
    font-size: 1.5rem;
}

.security-feature h6 {
    margin: 0.5rem 0;
    font-size: 0.875rem;
}

.security-feature small {
    font-size: 0.75rem;
}

#paymentForm .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}
</style>

<!-- Include Flutterwave JS SDK (for demo purposes) -->
<script src="https://checkout.flutterwave.com/v3.js"></script>

<!-- Include Google Maps API (using a placeholder API key - you should replace with your own) -->
<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap" async defer></script>

<script>
    // Initialize payment form validation
    document.addEventListener('DOMContentLoaded', function() {
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(e) {
                if (!paymentForm.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                paymentForm.classList.add('was-validated');
            });
        }

        // Initialize contact form validation
        const contactForms = document.querySelectorAll('.needs-validation');
        contactForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    });
    
    // Initialize Google Map
    function initMap() {
        <?php if (isset($property['lat']) && isset($property['lng']) && $property['lat'] && $property['lng']): ?>
            // Property coordinates
            const propertyLocation = {
                lat: <?php echo $property['lat']; ?>,
                lng: <?php echo $property['lng']; ?>
            };
            
            // Create map
            const map = new google.maps.Map(document.getElementById("propertyMap"), {
                zoom: 15,
                center: propertyLocation,
                mapTypeControl: true,
                scrollwheel: false,
                streetViewControl: true,
            });
            
            // Add marker for the property
            const marker = new google.maps.Marker({
                position: propertyLocation,
                map: map,
                title: "<?php echo addslashes($property['title']); ?>",
                animation: google.maps.Animation.DROP
            });
            
            // Add info window with property details
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="max-width:200px;">
                        <h6 style="margin:0 0 5px 0;"><?php echo addslashes($property['title']); ?></h6>
                        <p style="margin:0 0 5px 0;"><strong><?php echo addslashes($formattedPrice); ?></strong></p>
                        <p style="margin:0;"><?php echo addslashes($property['location']); ?></p>
                    </div>
                `
            });
            
            // Open info window when marker is clicked
            marker.addListener("click", () => {
                infoWindow.open(map, marker);
            });
            
            // Open info window by default
            infoWindow.open(map, marker);
        <?php endif; ?>
    }

    // Payment form enhancement
    function enhancePaymentForm() {
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            // Add loading state to submit button
            const submitBtn = paymentForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                paymentForm.addEventListener('submit', function() {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    submitBtn.disabled = true;
                });
            }

            // Credit card number formatting
            const cardNumber = document.getElementById('card-number');
            if (cardNumber) {
                cardNumber.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                    e.target.value = formattedValue;
                });
            }

            // Expiry date formatting
            const cardExpiry = document.getElementById('card-expiry');
            if (cardExpiry) {
                cardExpiry.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                    if (value.length >= 2) {
                        value = value.substring(0, 2) + '/' + value.substring(2, 4);
                    }
                    e.target.value = value;
                });
            }

            // CVV formatting
            const cardCvv = document.getElementById('card-cvv');
            if (cardCvv) {
                cardCvv.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                    e.target.value = value;
                });
            }

            // Card name formatting
            const cardName = document.getElementById('card-name');
            if (cardName) {
                cardName.addEventListener('input', function(e) {
                    e.target.value = e.target.value.toUpperCase();
                });
            }
        }
    }

    // Initialize payment form enhancement
    document.addEventListener('DOMContentLoaded', enhancePaymentForm);
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>