<?php
require_once 'includes/config.php';

echo "<h2>Setting up Property View Tracking</h2>";

try {
    // Check connection
    if (!$conn->ping()) {
        throw new Exception("Database connection failed");
    }
    echo "<p>✅ Database connected successfully</p>";
    
    // Create property_views table
    echo "<h3>Creating property_views table...</h3>";
    $createTableSql = "
    CREATE TABLE IF NOT EXISTS property_views (
        id INT AUTO_INCREMENT PRIMARY KEY,
        property_id INT NOT NULL,
        user_id INT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_view (property_id, user_id, ip_address)
    )";
    
    if ($conn->query($createTableSql)) {
        echo "<p>✅ property_views table created successfully</p>";
    } else {
        echo "<p>❌ Failed to create property_views table: " . $conn->error . "</p>";
    }
    
    // Create indexes for better performance
    echo "<h3>Creating indexes...</h3>";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_property_views_property_id ON property_views(property_id)",
        "CREATE INDEX IF NOT EXISTS idx_property_views_user_id ON property_views(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_property_views_viewed_at ON property_views(viewed_at)"
    ];
    
    foreach ($indexes as $indexSql) {
        if ($conn->query($indexSql)) {
            echo "<p>✅ Index created successfully</p>";
        } else {
            echo "<p>⚠️ Index creation warning: " . $conn->error . "</p>";
        }
    }
    
    // Update existing properties to have proper view counts
    echo "<h3>Initializing view counts...</h3>";
    $updateViewsSql = "UPDATE properties SET views = 0 WHERE views IS NULL OR views = 0";
    if ($conn->query($updateViewsSql)) {
        echo "<p>✅ View counts initialized</p>";
    } else {
        echo "<p>❌ Failed to initialize view counts: " . $conn->error . "</p>";
    }
    
    // Test the new functions
    echo "<h3>Testing new functions...</h3>";
    
    // Test getSellerTotalViews function
    if (function_exists('getSellerTotalViews')) {
        $testViews = getSellerTotalViews(2); // Test with seller ID 2
        echo "<p>✅ getSellerTotalViews function working: " . $testViews . " views</p>";
    } else {
        echo "<p>❌ getSellerTotalViews function not found</p>";
    }
    
    // Test getSellerTotalEarnings function
    if (function_exists('getSellerTotalEarnings')) {
        $testEarnings = getSellerTotalEarnings(2); // Test with seller ID 2
        echo "<p>✅ getSellerTotalEarnings function working: TZS " . number_format($testEarnings, 0) . "</p>";
    } else {
        echo "<p>❌ getSellerTotalEarnings function not found</p>";
    }
    
    // Check if sold properties are properly hidden
    echo "<h3>Checking sold properties...</h3>";
    $soldPropertiesSql = "SELECT COUNT(*) as sold_count FROM properties WHERE status = 'Sold'";
    $result = $conn->query($soldPropertiesSql);
    $soldCount = $result->fetch_assoc()['sold_count'];
    echo "<p>✅ Found " . $soldCount . " sold properties</p>";
    
    // Test that sold properties are excluded from public listings
    $availablePropertiesSql = "SELECT COUNT(*) as available_count FROM properties WHERE status = 'Available'";
    $result = $conn->query($availablePropertiesSql);
    $availableCount = $result->fetch_assoc()['available_count'];
    echo "<p>✅ Found " . $availableCount . " available properties for public viewing</p>";
    
    echo "<h3>✅ Property view tracking setup completed successfully!</h3>";
    echo "<p><strong>Summary of changes:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Created property_views table for unique view tracking</li>";
    echo "<li>✅ Added indexes for better performance</li>";
    echo "<li>✅ Updated view tracking to count unique users/IPs only</li>";
    echo "<li>✅ Updated earnings calculation to only count completed sales</li>";
    echo "<li>✅ Sold properties are now hidden from public view</li>";
    echo "<li>✅ Properties are marked as 'Sold' when payment is completed for sales</li>";
    echo "<li>✅ Properties are marked as 'Reserved' when payment is completed for rentals</li>";
    echo "</ul>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ You can now access the seller dashboard without errors</li>";
    echo "<li>✅ View counts will be tracked uniquely for each property</li>";
    echo "<li>✅ Earnings will only show from completed sales</li>";
    echo "<li>✅ Sold properties will be hidden from public view</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?> 