<?php
// Setup Password Reset - Add necessary database columns for password reset functionality
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Setup Password Reset</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔑 Password Reset Setup</h1>";

// Check if columns already exist
$columnsExist = false;
try {
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'reset_token'");
    if ($result->num_rows > 0) {
        $columnsExist = true;
    }
} catch (Exception $e) {
    // Table might not exist
}

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Setup Status</h3>";
echo "</div>";
echo "<div class='card-body'>";

if ($columnsExist) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>Setup Complete!</h5>";
    echo "<p class='mb-0'>Password reset columns already exist in the database.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>Setup Required</h5>";
    echo "<p class='mb-0'>Password reset columns need to be added to the database.</p>";
    echo "</div>";
    
    // Add the columns
    try {
        $sql1 = "ALTER TABLE users ADD COLUMN reset_token VARCHAR(64) NULL";
        $sql2 = "ALTER TABLE users ADD COLUMN reset_token_expires DATETIME NULL";
        $sql3 = "CREATE INDEX idx_reset_token ON users(reset_token)";
        $sql4 = "CREATE INDEX idx_reset_token_expires ON users(reset_token_expires)";
        
        $conn->query($sql1);
        $conn->query($sql2);
        
        // Try to create indexes (might fail if they already exist)
        try {
            $conn->query($sql3);
        } catch (Exception $e) {
            // Index might already exist
        }
        
        try {
            $conn->query($sql4);
        } catch (Exception $e) {
            // Index might already exist
        }
        
        echo "<div class='alert alert-success mt-3'>";
        echo "<h5><i class='fas fa-check-circle me-2'></i>Setup Successful!</h5>";
        echo "<p class='mb-0'>Password reset columns have been successfully added to the database.</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger mt-3'>";
        echo "<h5><i class='fas fa-times-circle me-2'></i>Setup Failed</h5>";
        echo "<p class='mb-0'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

echo "</div></div>";

// Test the forgot password functionality
echo "<div class='card mt-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-test-tube me-2'></i>Test Password Reset</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>Password Reset Features:</h5>";
echo "<ul class='list-group list-group-flush'>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Email-based password reset</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Secure token generation</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Token expiration (1 hour)</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Password strength validation</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Professional email templates</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>Security Features:</h5>";
echo "<ul class='list-group list-group-flush'>";
echo "<li class='list-group-item'><i class='fas fa-shield-alt text-success me-2'></i>Secure token generation</li>";
echo "<li class='list-group-item'><i class='fas fa-clock text-success me-2'></i>Time-limited tokens</li>";
echo "<li class='list-group-item'><i class='fas fa-user-secret text-success me-2'></i>No email enumeration</li>";
echo "<li class='list-group-item'><i class='fas fa-lock text-success me-2'></i>Password hashing</li>";
echo "<li class='list-group-item'><i class='fas fa-trash text-success me-2'></i>Token cleanup</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Action buttons
echo "<div class='text-center mt-4'>";
echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='forgot-password.php' class='btn btn-primary' target='_blank'><i class='fas fa-key me-2'></i>Test Forgot Password</a>";
echo "<a href='login.php' class='btn btn-success' target='_blank'><i class='fas fa-sign-in-alt me-2'></i>Test Login Page</a>";
echo "<a href='index.php' class='btn btn-secondary'><i class='fas fa-home me-2'></i>Homepage</a>";
echo "</div>";
echo "</div>";

// Instructions
echo "<div class='card mt-4'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-info-circle me-2'></i>How to Use</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>For Users:</h5>";
echo "<ol>";
echo "<li>Go to the <a href='login.php' target='_blank'>login page</a></li>";
echo "<li>Click on 'Forgot your password?' link</li>";
echo "<li>Enter your email address</li>";
echo "<li>Check your email for the reset link</li>";
echo "<li>Click the link and set a new password</li>";
echo "</ol>";

echo "<h5 class='mt-4'>For Administrators:</h5>";
echo "<ul>";
echo "<li><strong>Email Configuration:</strong> Ensure email settings are configured in <code>includes/email_helper.php</code></li>";
echo "<li><strong>Security:</strong> Reset tokens expire after 1 hour automatically</li>";
echo "<li><strong>Cleanup:</strong> Expired tokens are cleaned up automatically</li>";
echo "<li><strong>Monitoring:</strong> Check email logs for delivery issues</li>";
echo "</ul>";

echo "</div></div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
