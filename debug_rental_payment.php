<?php
// Debug Rental Payment - Comprehensive debugging tool for rental payment issues
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/rental_functions.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Rental Payment</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔍 Rental Payment Debug Tool</h1>";

// Step 1: Database Tables Check
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Tables Status</h3>";
echo "</div>";
echo "<div class='card-body'>";

$tables = [
    'properties' => 'Main properties table',
    'users' => 'User accounts table',
    'rental_payments' => 'Rental payment transactions',
    'property_rentals' => 'Active rental tracking',
    'messages' => 'User messaging system'
];

$allTablesExist = true;
echo "<div class='row'>";
foreach ($tables as $table => $description) {
    $exists = $conn->query("SHOW TABLES LIKE '$table'")->num_rows > 0;
    if (!$exists) $allTablesExist = false;
    
    echo "<div class='col-md-6 mb-2'>";
    echo "<div class='d-flex justify-content-between align-items-center'>";
    echo "<span><strong>$table:</strong> $description</span>";
    echo "<span class='badge bg-" . ($exists ? 'success' : 'danger') . "'>" . ($exists ? 'EXISTS' : 'MISSING') . "</span>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

if (!$allTablesExist) {
    echo "<div class='alert alert-warning mt-3'>";
    echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Missing Tables Detected</h6>";
    echo "<p class='mb-2'>Some required tables are missing. This will cause rental payment failures.</p>";
    echo "<a href='quick_setup_rental.php' class='btn btn-warning'>Quick Setup</a>";
    echo "</div>";
}

echo "</div></div>";

// Step 2: Test Rental Functions
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-cog me-2'></i>Function Tests</h3>";
echo "</div>";
echo "<div class='card-body'>";

$functionTests = [];

// Test getRentalDurations
try {
    $durations = getRentalDurations();
    $functionTests['getRentalDurations'] = ['status' => 'success', 'result' => count($durations) . ' durations available'];
} catch (Exception $e) {
    $functionTests['getRentalDurations'] = ['status' => 'error', 'result' => $e->getMessage()];
}

// Test calculateTotalRent
try {
    $total = calculateTotalRent(50000, 6);
    $functionTests['calculateTotalRent'] = ['status' => 'success', 'result' => 'TZS ' . number_format($total, 2) . ' for 6 months'];
} catch (Exception $e) {
    $functionTests['calculateTotalRent'] = ['status' => 'error', 'result' => $e->getMessage()];
}

// Test calculateRentalEndDate
try {
    $endDate = calculateRentalEndDate('2024-01-01', 6);
    $functionTests['calculateRentalEndDate'] = ['status' => 'success', 'result' => $endDate];
} catch (Exception $e) {
    $functionTests['calculateRentalEndDate'] = ['status' => 'error', 'result' => $e->getMessage()];
}

// Test isPropertyRented
try {
    $rented = isPropertyRented(1);
    $functionTests['isPropertyRented'] = ['status' => 'success', 'result' => $rented ? 'Property 1 is rented' : 'Property 1 is available'];
} catch (Exception $e) {
    $functionTests['isPropertyRented'] = ['status' => 'error', 'result' => $e->getMessage()];
}

echo "<div class='row'>";
foreach ($functionTests as $function => $test) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>";
    echo "<i class='fas fa-" . ($test['status'] === 'success' ? 'check text-success' : 'times text-danger') . " me-2'></i>";
    echo "$function()";
    echo "</h6>";
    echo "<p class='card-text text-muted'>" . htmlspecialchars($test['result']) . "</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "</div></div>";

// Step 3: Test Data Validation
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'><i class='fas fa-check-double me-2'></i>Data Validation Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Test rental payment parameters
$testParams = [
    'propertyId' => 1,
    'tenantId' => 1,
    'landlordId' => 1,
    'monthlyRate' => 50000.00,
    'duration' => 6,
    'totalAmount' => 291000.00,
    'transactionId' => 'TEST_DEBUG_' . time(),
    'startDate' => date('Y-m-d')
];

echo "<h5>Test Parameters:</h5>";
echo "<div class='row'>";
foreach ($testParams as $param => $value) {
    echo "<div class='col-md-6 mb-2'>";
    echo "<div class='d-flex justify-content-between'>";
    echo "<span><strong>$param:</strong></span>";
    echo "<span class='text-muted'>" . htmlspecialchars($value) . "</span>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

// Validate parameters
$validationErrors = [];

if (!$testParams['propertyId'] || !is_numeric($testParams['propertyId'])) {
    $validationErrors[] = "Invalid property ID";
}

if (!$testParams['tenantId'] || !is_numeric($testParams['tenantId'])) {
    $validationErrors[] = "Invalid tenant ID";
}

if (!$testParams['landlordId'] || !is_numeric($testParams['landlordId'])) {
    $validationErrors[] = "Invalid landlord ID";
}

if (!$testParams['monthlyRate'] || !is_numeric($testParams['monthlyRate']) || $testParams['monthlyRate'] <= 0) {
    $validationErrors[] = "Invalid monthly rate";
}

if (!$testParams['duration'] || !is_numeric($testParams['duration']) || $testParams['duration'] <= 0) {
    $validationErrors[] = "Invalid duration";
}

if (!$testParams['totalAmount'] || !is_numeric($testParams['totalAmount']) || $testParams['totalAmount'] <= 0) {
    $validationErrors[] = "Invalid total amount";
}

if (empty($testParams['transactionId'])) {
    $validationErrors[] = "Missing transaction ID";
}

if (empty($testParams['startDate']) || !strtotime($testParams['startDate'])) {
    $validationErrors[] = "Invalid start date";
}

if (empty($validationErrors)) {
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle me-2'></i>Validation Passed</h6>";
    echo "<p class='mb-0'>All test parameters are valid for rental payment processing.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Validation Errors</h6>";
    echo "<ul class='mb-0'>";
    foreach ($validationErrors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "</div></div>";

// Step 4: Test Rental Payment Processing
if ($allTablesExist && empty($validationErrors)) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h3 class='mb-0'><i class='fas fa-test-tube me-2'></i>Rental Payment Test</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-info-circle me-2'></i>Ready for Testing</h6>";
    echo "<p class='mb-0'>All prerequisites are met. You can now test the rental payment processing.</p>";
    echo "</div>";
    
    echo "<div class='d-grid gap-2'>";
    echo "<a href='simple_rental_payment.php?" . http_build_query($testParams) . "' class='btn btn-success' target='_blank'>";
    echo "<i class='fas fa-play me-2'></i>Test Rental Payment Processing";
    echo "</a>";
    echo "</div>";
    
    echo "</div></div>";
}

// Step 5: Available Rental Properties
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-home me-2'></i>Available Rental Properties</h3>";
echo "</div>";
echo "<div class='card-body'>";

$rentalProperties = $conn->query("SELECT id, title, price, location, listing_type, status FROM properties WHERE listing_type = 'Rent' AND status = 'Available' ORDER BY id LIMIT 3")->fetch_all(MYSQLI_ASSOC);

if (empty($rentalProperties)) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>No rental properties found for testing.";
    echo "</div>";
} else {
    echo "<div class='row'>";
    foreach ($rentalProperties as $prop) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>Property #{$prop['id']}</h6>";
        echo "<p class='card-text'>" . htmlspecialchars(substr($prop['title'], 0, 30)) . "...</p>";
        echo "<p class='text-success'><strong>TZS " . number_format($prop['price']) . "/month</strong></p>";
        echo "<span class='badge bg-success mb-2'>{$prop['status']}</span>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='property.php?id={$prop['id']}' class='btn btn-primary btn-sm' target='_blank'>View Property</a>";
        echo "<a href='pay_rent.php?property_id={$prop['id']}' class='btn btn-success btn-sm' target='_blank'>Test Rental Form</a>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div></div>";

// Action Buttons
echo "<div class='text-center'>";
echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='test_rental_system.php' class='btn btn-primary'><i class='fas fa-test me-2'></i>Full System Test</a>";
echo "<a href='quick_setup_rental.php' class='btn btn-warning'><i class='fas fa-magic me-2'></i>Quick Setup</a>";
echo "<a href='index.php' class='btn btn-success'><i class='fas fa-home me-2'></i>Homepage</a>";
echo "</div>";
echo "</div>";

// Final Status
echo "<div class='text-center mt-4'>";
if ($allTablesExist && empty($validationErrors)) {
    echo "<div class='alert alert-success'>";
    echo "<h4><i class='fas fa-check-circle me-2'></i>✅ RENTAL SYSTEM READY!</h4>";
    echo "<p class='mb-0'>All components are working correctly. Rental payment processing should work without errors.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>⚠️ SETUP REQUIRED!</h4>";
    echo "<p class='mb-0'>Some issues were detected. Please resolve them before testing rental payments.</p>";
    echo "</div>";
}
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
