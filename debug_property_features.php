<?php
// Debug Property Features - Complete Analysis
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Property Features</title>";
echo "<style>
body{font-family:Arial,sans-serif;margin:40px;} 
.success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}
.debug-section{background:#f8f9fa; padding:20px; margin:20px 0; border-radius:10px; border-left:5px solid #007bff;}
pre{background:#f1f1f1; padding:15px; border-radius:5px; overflow-x:auto;}
table{width:100%; border-collapse:collapse; margin:15px 0;}
th,td{padding:8px; border:1px solid #ddd; text-align:left;}
th{background:#f0f0f0;}
</style>";
echo "</head><body>";

echo "<h1>🔍 Debug Property Features</h1>";

// Step 1: Check Database Structure
echo "<div class='debug-section'>";
echo "<h2>📊 Step 1: Database Structure Analysis</h2>";

$result = $conn->query("DESCRIBE properties");
$columns = [];
echo "<table>";
echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
while ($row = $result->fetch_assoc()) {
    $columns[] = $row['Field'];
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>";
}
echo "</table>";

$requiredFeatures = ['has_electricity', 'has_water', 'has_fence', 'has_gym', 'has_air_condition', 'has_security', 'has_pool'];
$missingFeatures = [];
foreach ($requiredFeatures as $feature) {
    if (!in_array($feature, $columns)) {
        $missingFeatures[] = $feature;
    }
}

if (empty($missingFeatures)) {
    echo "<p class='success'>✅ All feature columns exist in database</p>";
} else {
    echo "<p class='error'>❌ Missing columns: " . implode(', ', $missingFeatures) . "</p>";
}
echo "</div>";

// Step 2: Test getPropertyById Function
echo "<div class='debug-section'>";
echo "<h2>🔧 Step 2: Test getPropertyById Function</h2>";

$propertyId = 1;
echo "<p><strong>Testing getPropertyById($propertyId)...</strong></p>";

$property = getPropertyById($propertyId);

if ($property) {
    echo "<p class='success'>✅ Property data retrieved successfully</p>";
    echo "<h3>Property Data:</h3>";
    echo "<table>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($property as $key => $value) {
        echo "<tr>";
        echo "<td><strong>$key</strong></td>";
        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Feature Analysis:</h3>";
    foreach ($requiredFeatures as $feature) {
        $value = isset($property[$feature]) ? $property[$feature] : 'NOT SET';
        $status = '';
        if ($value === 'NOT SET') {
            $status = "<span class='error'>❌ Not in result</span>";
        } elseif ($value == 1) {
            $status = "<span class='success'>✅ Yes</span>";
        } else {
            $status = "<span class='info'>❌ No</span>";
        }
        echo "<p><strong>$feature:</strong> $value $status</p>";
    }
} else {
    echo "<p class='error'>❌ No property found with ID $propertyId</p>";
}
echo "</div>";

// Step 3: Direct Database Query Test
echo "<div class='debug-section'>";
echo "<h2>🗄️ Step 3: Direct Database Query Test</h2>";

$directQuery = "SELECT id, title, has_electricity, has_water, has_fence, has_gym, has_air_condition, has_security, has_pool FROM properties WHERE id = $propertyId";
echo "<p><strong>Direct Query:</strong></p>";
echo "<pre>$directQuery</pre>";

$directResult = $conn->query($directQuery);
if ($directResult && $directResult->num_rows > 0) {
    $directProperty = $directResult->fetch_assoc();
    echo "<p class='success'>✅ Direct query successful</p>";
    echo "<table>";
    echo "<tr><th>Feature</th><th>Value</th><th>Display</th></tr>";
    foreach ($requiredFeatures as $feature) {
        $value = isset($directProperty[$feature]) ? $directProperty[$feature] : 'NULL';
        $display = ($value == 1) ? 'Yes' : 'No';
        echo "<tr>";
        echo "<td>$feature</td>";
        echo "<td>$value</td>";
        echo "<td>$display</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>❌ Direct query failed or no results</p>";
}
echo "</div>";

// Step 4: Add Test Data
echo "<div class='debug-section'>";
echo "<h2>🧪 Step 4: Add Test Data</h2>";

if (empty($missingFeatures)) {
    $updateQuery = "UPDATE properties SET 
                    has_electricity = 1, 
                    has_water = 1, 
                    has_fence = 0, 
                    has_gym = 1, 
                    has_air_condition = 0, 
                    has_security = 1, 
                    has_pool = 0 
                    WHERE id = $propertyId";
    
    echo "<p><strong>Adding test data:</strong></p>";
    echo "<pre>$updateQuery</pre>";
    
    if ($conn->query($updateQuery)) {
        echo "<p class='success'>✅ Test data added successfully</p>";
        
        // Verify the update
        $verifyResult = $conn->query("SELECT has_electricity, has_water, has_fence, has_gym, has_air_condition, has_security, has_pool FROM properties WHERE id = $propertyId");
        if ($verifyResult && $verifyResult->num_rows > 0) {
            $verifyData = $verifyResult->fetch_assoc();
            echo "<h3>Verification:</h3>";
            echo "<table>";
            echo "<tr><th>Feature</th><th>Value</th><th>Status</th></tr>";
            foreach ($requiredFeatures as $feature) {
                $value = $verifyData[$feature];
                $status = ($value == 1) ? "<span class='success'>✅ Yes</span>" : "<span class='info'>❌ No</span>";
                echo "<tr><td>$feature</td><td>$value</td><td>$status</td></tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ Failed to add test data: " . $conn->error . "</p>";
    }
} else {
    echo "<p class='warning'>⚠️ Cannot add test data - missing columns need to be added first</p>";
}
echo "</div>";

// Step 5: Test Property Display
echo "<div class='debug-section'>";
echo "<h2>🖼️ Step 5: Property Display Test</h2>";

if ($property) {
    echo "<h3>How features will display on property page:</h3>";
    echo "<div style='background:white; padding:20px; border:1px solid #ddd; border-radius:8px;'>";
    echo "<h4>Additional Features:</h4>";
    echo "<ul style='list-style:none; padding:0;'>";
    
    $featureIcons = [
        'has_electricity' => 'fas fa-bolt',
        'has_water' => 'fas fa-tint', 
        'has_fence' => 'fas fa-border-all',
        'has_gym' => 'fas fa-dumbbell',
        'has_air_condition' => 'fas fa-snowflake',
        'has_security' => 'fas fa-shield-alt',
        'has_pool' => 'fas fa-swimming-pool'
    ];
    
    $featureNames = [
        'has_electricity' => 'Electricity',
        'has_water' => 'Water',
        'has_fence' => 'Fence', 
        'has_gym' => 'Gym',
        'has_air_condition' => 'Air Conditioning',
        'has_security' => 'Security',
        'has_pool' => 'Swimming Pool'
    ];
    
    foreach ($requiredFeatures as $feature) {
        $hasFeature = isset($property[$feature]) && $property[$feature];
        $icon = $featureIcons[$feature];
        $name = $featureNames[$feature];
        $status = $hasFeature ? 'Yes' : 'No';
        $badgeClass = $hasFeature ? 'bg-success' : 'bg-secondary';
        
        echo "<li style='display:flex; justify-content:space-between; align-items:center; padding:8px 0; border-bottom:1px solid #eee;'>";
        echo "<span><i class='$icon me-2'></i> $name</span>";
        echo "<span class='badge $badgeClass'>$status</span>";
        echo "</li>";
    }
    echo "</ul>";
    echo "</div>";
}
echo "</div>";

echo "<div style='background:#e8f5e8; padding:30px; border-radius:15px; margin:30px 0; text-align:center;'>";
echo "<h2>🎯 Summary & Next Steps</h2>";

if (empty($missingFeatures)) {
    echo "<p class='success' style='font-size:18px;'><strong>✅ FEATURES ARE READY TO DISPLAY!</strong></p>";
    echo "<p>✅ Database columns exist</p>";
    echo "<p>✅ Backend function retrieves data</p>";
    echo "<p>✅ Frontend display code is updated</p>";
    echo "<p>✅ Test data has been added</p>";
} else {
    echo "<p class='warning' style='font-size:18px;'><strong>⚠️ MISSING DATABASE COLUMNS</strong></p>";
    echo "<p>❌ Need to add: " . implode(', ', $missingFeatures) . "</p>";
}
echo "</div>";

echo "<div style='text-align:center; margin:30px 0;'>";
if (!empty($missingFeatures)) {
    echo "<a href='fix_property_features_display.php' style='background:#dc3545; color:white; padding:12px 24px; text-decoration:none; border-radius:8px; margin:10px; font-weight:bold;'>🔧 Fix Database First</a>";
}
echo "<a href='property.php?id=$propertyId' style='background:#007bff; color:white; padding:12px 24px; text-decoration:none; border-radius:8px; margin:10px;'>👁️ View Property Page</a>";
echo "<a href='add_property.php' style='background:#28a745; color:white; padding:12px 24px; text-decoration:none; border-radius:8px; margin:10px;'>🏠 Add New Property</a>";
echo "</div>";

$conn->close();
echo "</body></html>";
?>
