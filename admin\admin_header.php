<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is an admin
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header("Location: login.php");
    exit;
}

// Set base URL
$admin_base_url = '../';

// Function to get current page name for active navigation highlighting
function getCurrentAdminPage() {
    $currentPage = basename($_SERVER['PHP_SELF']);
    return $currentPage;
}

// Function to check if a navigation link should be active
function isActiveAdminPage($page) {
    $currentPage = getCurrentAdminPage();
    return $currentPage === $page ? ' active' : '';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>TX Properties Admin Panel</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Same as main site -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Main Site CSS for theme consistency -->
    <link href="<?php echo $admin_base_url; ?>assets/css/style.css" rel="stylesheet">

    <!-- Admin-specific CSS -->
    <link href="<?php echo $admin_base_url; ?>assets/css/admin.css" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Admin Navigation with Main Site Theme -->
    <nav class="navbar navbar-expand-lg admin-navbar sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand admin-brand" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="brand-text">TX Properties</span>
                <span class="admin-badge">Admin</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar" aria-controls="adminNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="adminNavbar">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link admin-nav-link<?php echo isActiveAdminPage('index.php'); ?>" href="index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link admin-nav-link<?php echo isActiveAdminPage('properties.php'); ?>" href="properties.php">
                            <i class="fas fa-home me-2"></i>Properties
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link admin-nav-link<?php echo isActiveAdminPage('users.php'); ?>" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link admin-nav-link<?php echo isActiveAdminPage('settings.php'); ?>" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item me-3">
                        <a class="nav-link admin-nav-link" href="<?php echo $admin_base_url; ?>index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Website
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle admin-nav-link" href="#" id="adminUserDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield me-2"></i>
                            <span class="d-none d-md-inline"><?php echo isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : 'Administrator'; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end admin-dropdown" aria-labelledby="adminUserDropdown">
                            <li class="dropdown-header">
                                <i class="fas fa-user-circle me-2"></i>Admin Panel
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item admin-dropdown-item" href="settings.php">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item admin-dropdown-item" href="<?php echo $admin_base_url; ?>index.php" target="_blank">
                                    <i class="fas fa-globe me-2"></i>View Website
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item admin-dropdown-item text-danger" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Admin Content Area with Main Site Theme -->
    <div class="admin-content-wrapper">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="admin-page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb admin-breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="index.php"><i class="fas fa-home me-1"></i>Admin</a>
                                </li>
                                <?php if (isset($breadcrumbs) && is_array($breadcrumbs)): ?>
                                    <?php foreach ($breadcrumbs as $crumb): ?>
                                        <?php if (isset($crumb['url'])): ?>
                                            <li class="breadcrumb-item">
                                                <a href="<?php echo $crumb['url']; ?>"><?php echo $crumb['title']; ?></a>
                                            </li>
                                        <?php else: ?>
                                            <li class="breadcrumb-item active" aria-current="page">
                                                <?php echo $crumb['title']; ?>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <main class="admin-main-content"><?php // Content will be inserted here by individual pages ?>