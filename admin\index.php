<?php
session_start();
// Include necessary files
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Set page variables for header
$pageTitle = 'Dashboard';
$breadcrumbs = [
    ['title' => 'Dashboard']
];

// Get statistics
// Count total properties
$sql = "SELECT COUNT(*) as total FROM properties";
$result = $conn->query($sql);
$totalProperties = $result->fetch_assoc()['total'];

// Count total users
$sql = "SELECT COUNT(*) as total FROM users";
$result = $conn->query($sql);
$totalUsers = $result->fetch_assoc()['total'];



// Get latest properties
$sql = "SELECT p.*, u.username as seller_name FROM properties p 
        JOIN users u ON p.seller_id = u.id 
        ORDER BY p.created_at DESC LIMIT 5";
$result = $conn->query($sql);
$latestProperties = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $latestProperties[] = $row;
    }
}

// Include admin header
include_once 'admin_header.php';
?>

<!-- Admin Dashboard Content -->
<section class="admin-dashboard-header">
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="admin-page-title mb-2">
                <i class="fas fa-tachometer-alt me-3"></i>Admin Dashboard
            </h1>
            <p class="text-muted mb-0">
                Welcome to the TX Properties administration panel. Manage your properties, users, and settings from here.
            </p>
        </div>
        <div class="col-auto">
            <div class="admin-quick-actions">
                <a href="properties.php" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-1"></i>Add Property
                </a>
                <a href="users.php" class="btn btn-outline-primary">
                    <i class="fas fa-users me-1"></i>Manage Users
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Overview Section -->
<section class="admin-stats-section">
    <div class="row align-items-center mb-3">
        <div class="col">
            <h3 class="mb-0">
                <i class="fas fa-chart-bar me-2"></i>Overview Statistics
            </h3>
        </div>
    </div>
    <div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card admin-stats-card">
            <div class="icon">
                <i class="fas fa-home"></i>
            </div>
            <div class="number"><?php echo $totalProperties; ?></div>
            <div class="label">Total Properties</div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card admin-stats-card" style="background: var(--gradient-accent) !important;">
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="number"><?php echo $totalUsers; ?></div>
            <div class="label">Total Users</div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card admin-stats-card" style="background: var(--secondary-color) !important;">
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="number">
                <?php
                // Count available properties
                $sql = "SELECT COUNT(*) as total FROM properties WHERE status = 'Available'";
                $result = $conn->query($sql);
                echo $result->fetch_assoc()['total'];
                ?>
            </div>
            <div class="label">Available Properties</div>
        </div>
    </div>
</section>

<!-- Latest Properties Section -->
<section class="admin-properties-section">
    <div class="row align-items-center mb-3">
        <div class="col">
            <h3 class="mb-0">
                <i class="fas fa-home me-2"></i>Latest Properties
            </h3>
            <p class="text-muted mb-0">Recently added property listings</p>
        </div>
        <div class="col-auto">
            <a href="properties.php" class="btn btn-primary">
                <i class="fas fa-eye me-1"></i>View All Properties
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card admin-card">
                <div class="card-body admin-card-body p-0">
                    <?php if (empty($latestProperties)): ?>
                        <div class="p-4 text-center">
                            <div class="alert alert-info border-0">
                                <i class="fas fa-info-circle me-2"></i>No properties found.
                                <div class="mt-2">
                                    <a href="properties.php" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>Add First Property
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table admin-table mb-0">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>ID</th>
                                    <th><i class="fas fa-home me-1"></i>Title</th>
                                    <th><i class="fas fa-tag me-1"></i>Type</th>
                                    <th><i class="fas fa-dollar-sign me-1"></i>Price</th>
                                    <th><i class="fas fa-map-marker-alt me-1"></i>Location</th>
                                    <th><i class="fas fa-user me-1"></i>Seller</th>
                                    <th><i class="fas fa-info-circle me-1"></i>Status</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                                <tbody>
                                    <?php foreach ($latestProperties as $property): ?>
                                        <tr>
                                            <td><?php echo $property['id']; ?></td>
                                            <td><?php echo htmlspecialchars($property['title']); ?></td>
                                            <td><?php echo $property['property_type']; ?></td>
                                            <td><?php echo formatPrice($property['price'], $property['listing_type']); ?></td>
                                            <td><?php echo htmlspecialchars($property['location']); ?></td>
                                            <td><?php echo htmlspecialchars($property['seller_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $property['status'] == 'Available' ? 'bg-success' : 'bg-secondary'; ?>">
                                                    <?php echo $property['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="../property.php?id=<?php echo $property['id']; ?>"
                                                       class="btn btn-sm btn-outline-primary" target="_blank"
                                                       title="View Property">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_property.php?id=<?php echo $property['id']; ?>"
                                                       class="btn btn-sm btn-primary"
                                                       title="Edit Property">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// Include admin footer
include_once 'admin_footer.php';
?> 