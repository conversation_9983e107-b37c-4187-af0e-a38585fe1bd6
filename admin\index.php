<?php
session_start();
// Include necessary files
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Get statistics
// Count total properties
$sql = "SELECT COUNT(*) as total FROM properties";
$result = $conn->query($sql);
$totalProperties = $result->fetch_assoc()['total'];

// Count total users
$sql = "SELECT COUNT(*) as total FROM users";
$result = $conn->query($sql);
$totalUsers = $result->fetch_assoc()['total'];

// Count unread contact messages
$sql = "SELECT COUNT(*) as total FROM contacts WHERE status = 'unread'";
$result = $conn->query($sql);
$unreadMessages = $result ? $result->fetch_assoc()['total'] : 0;

// Get latest properties
$sql = "SELECT p.*, u.username as seller_name FROM properties p 
        JOIN users u ON p.seller_id = u.id 
        ORDER BY p.created_at DESC LIMIT 5";
$result = $conn->query($sql);
$latestProperties = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $latestProperties[] = $row;
    }
}

// Include admin header
include_once 'admin_header.php';
?>

<div class="container-fluid py-4">
    <!-- Stats Row -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Properties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalProperties; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-home fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Users</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalUsers; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Unread Messages
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $unreadMessages; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Available Properties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php 
                                // Count available properties
                                $sql = "SELECT COUNT(*) as total FROM properties WHERE status = 'Available'";
                                $result = $conn->query($sql);
                                echo $result->fetch_assoc()['total']; 
                                ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Latest Properties Row -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Latest Properties</h6>
                    <a href="properties.php" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($latestProperties)): ?>
                        <div class="alert alert-info">No properties found.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Price</th>
                                        <th>Location</th>
                                        <th>Seller</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($latestProperties as $property): ?>
                                        <tr>
                                            <td><?php echo $property['id']; ?></td>
                                            <td><?php echo htmlspecialchars($property['title']); ?></td>
                                            <td><?php echo $property['property_type']; ?></td>
                                            <td><?php echo formatPrice($property['price'], $property['listing_type']); ?></td>
                                            <td><?php echo htmlspecialchars($property['location']); ?></td>
                                            <td><?php echo htmlspecialchars($property['seller_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $property['status'] == 'Available' ? 'bg-success' : 'bg-secondary'; ?>">
                                                    <?php echo $property['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="../property.php?id=<?php echo $property['id']; ?>" class="btn btn-sm btn-info" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_property.php?id=<?php echo $property['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Messages Row -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Contact Messages</h6>
                    <a href="contact_messages.php" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php 
                    // Get recent contact messages
                    $sql = "SELECT * FROM contacts ORDER BY created_at DESC LIMIT 5";
                    $result = $conn->query($sql);
                    $messages = [];
                    
                    if ($result) {
                        while ($row = $result->fetch_assoc()) {
                            $messages[] = $row;
                        }
                    }
                    
                    if (empty($messages)): 
                    ?>
                        <div class="alert alert-info">No contact messages found.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($messages as $msg): ?>
                                        <tr class="<?php echo $msg['status'] == 'unread' ? 'table-primary' : ''; ?>">
                                            <td><?php echo $msg['id']; ?></td>
                                            <td><?php echo htmlspecialchars($msg['name']); ?></td>
                                            <td><?php echo htmlspecialchars($msg['email']); ?></td>
                                            <td><?php echo htmlspecialchars($msg['subject']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $msg['status'] == 'unread' ? 'bg-primary' : 'bg-secondary'; ?>">
                                                    <?php echo ucfirst($msg['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y H:i', strtotime($msg['created_at'])); ?></td>
                                            <td>
                                                <a href="contact_messages.php?action=view&id=<?php echo $msg['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include admin footer
include_once 'admin_footer.php';
?> 