<?php
// Fix Property Features Display - Complete Solution
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Fix Property Features Display</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}</style>";
echo "</head><body>";

echo "<h1>🔧 Fix Property Features Display</h1>";

// Step 1: Check and Add Database Columns
echo "<h2>📊 Step 1: Database Columns Check</h2>";

$requiredColumns = ['has_electricity', 'has_water', 'has_fence'];
$result = $conn->query("DESCRIBE properties");
$existingColumns = [];
while ($row = $result->fetch_assoc()) {
    $existingColumns[] = $row['Field'];
}

$missingColumns = [];
foreach ($requiredColumns as $column) {
    if (in_array($column, $existingColumns)) {
        echo "<p class='success'>✅ $column - EXISTS</p>";
    } else {
        echo "<p class='error'>❌ $column - MISSING</p>";
        $missingColumns[] = $column;
    }
}

// Add missing columns
if (!empty($missingColumns)) {
    echo "<h3>Adding Missing Columns...</h3>";
    
    $alterSQL = "ALTER TABLE properties ";
    $alterParts = [];
    foreach ($missingColumns as $column) {
        $alterParts[] = "ADD $column TINYINT(1) DEFAULT 0";
    }
    $alterSQL .= implode(', ', $alterParts);
    
    echo "<p><strong>Executing:</strong> <code>$alterSQL</code></p>";
    
    if ($conn->query($alterSQL)) {
        echo "<p class='success'>✅ Successfully added missing columns!</p>";
    } else {
        echo "<p class='error'>❌ Error: " . $conn->error . "</p>";
    }
} else {
    echo "<p class='success'>✅ All required columns exist!</p>";
}

// Step 2: Test Data Retrieval
echo "<h2>🔍 Step 2: Test Data Retrieval</h2>";

$testQuery = "SELECT id, title, has_electricity, has_water, has_fence FROM properties LIMIT 3";
$testResult = $conn->query($testQuery);

if ($testResult) {
    echo "<p class='success'>✅ Query executed successfully</p>";
    echo "<table border='1' style='border-collapse:collapse; margin:15px 0;'>";
    echo "<tr style='background:#f0f0f0;'><th style='padding:10px;'>ID</th><th style='padding:10px;'>Title</th><th style='padding:10px;'>Electricity</th><th style='padding:10px;'>Water</th><th style='padding:10px;'>Fence</th></tr>";
    
    if ($testResult->num_rows > 0) {
        while ($row = $testResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding:8px;'>" . $row['id'] . "</td>";
            echo "<td style='padding:8px;'>" . htmlspecialchars(substr($row['title'], 0, 30)) . "</td>";
            echo "<td style='padding:8px;'>" . ($row['has_electricity'] ? 'Yes' : 'No') . "</td>";
            echo "<td style='padding:8px;'>" . ($row['has_water'] ? 'Yes' : 'No') . "</td>";
            echo "<td style='padding:8px;'>" . ($row['has_fence'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
    } else {
        echo "<tr><td colspan='5' style='padding:8px; text-align:center;'>No properties found</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>❌ Query failed: " . $conn->error . "</p>";
}

// Step 3: Add Test Data
echo "<h2>🧪 Step 3: Add Test Data</h2>";

if ($testResult && $testResult->num_rows > 0) {
    $updateQuery = "UPDATE properties SET has_electricity = 1, has_water = 1, has_fence = 0 WHERE id = 1";
    if ($conn->query($updateQuery)) {
        echo "<p class='success'>✅ Updated property ID 1 with test feature data</p>";
    } else {
        echo "<p class='error'>❌ Failed to update test data: " . $conn->error . "</p>";
    }
}

echo "<div style='background:#f0f8ff; padding:20px; border-radius:10px; margin:30px 0;'>";
echo "<h2>📋 Next Steps</h2>";
echo "<p>1. Database columns have been checked/added</p>";
echo "<p>2. Now we need to fix the backend data fetching</p>";
echo "<p>3. Then fix the frontend display</p>";
echo "</div>";

echo "<div style='text-align:center; margin:30px 0;'>";
echo "<a href='property.php?id=1' style='background:#007bff; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Test Property Display</a>";
echo "<a href='index.php' style='background:#28a745; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Go to Homepage</a>";
echo "</div>";

$conn->close();
echo "</body></html>";
?>
