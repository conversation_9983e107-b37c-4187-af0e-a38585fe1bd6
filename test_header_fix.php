<?php
// Test Header Fix - Verify no more "headers already sent" errors
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Header Fix</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔧 Header Fix Verification</h1>";

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-check me-2'></i>Fixed Files</h3>";
echo "</div>";
echo "<div class='card-body'>";

$fixedFiles = [
    'property.php' => 'Property details page',
    'compose.php' => 'Compose message page',
    'reply_message.php' => 'Reply to message page',
    'inbox.php' => 'Seller inbox page',
    'pay.php' => 'Payment page',
    'view_message.php' => 'View message page',
    'profile.php' => 'User profile page',
    'my_properties.php' => 'My properties page'
];

echo "<div class='row'>";
foreach ($fixedFiles as $file => $description) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'><i class='fas fa-file-code me-2'></i>$file</h6>";
    echo "<p class='card-text text-muted'>$description</p>";
    echo "<span class='badge bg-success'><i class='fas fa-check me-1'></i>Fixed</span>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "</div></div>";

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-info me-2'></i>What Was Fixed</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>❌ Before (Causing Errors):</h5>";
echo "<pre class='bg-light p-3 border rounded'>";
echo "require_once 'includes/header.php';\n";
echo "// HTML output starts here\n";
echo "\n";
echo "if (!isLoggedIn()) {\n";
echo "    redirect('login.php'); // ERROR!\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>✅ After (Fixed):</h5>";
echo "<pre class='bg-light p-3 border rounded'>";
echo "require_once 'includes/config.php';\n";
echo "\n";
echo "if (!isLoggedIn()) {\n";
echo "    redirect('login.php'); // OK!\n";
echo "}\n";
echo "\n";
echo "require_once 'includes/header.php';\n";
echo "// HTML output starts here";
echo "</pre>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-3'>";
echo "<h5><i class='fas fa-lightbulb me-2'></i>Key Fix:</h5>";
echo "<p class='mb-0'>Moved all <code>redirect()</code> calls to happen <strong>before</strong> including <code>header.php</code>, which prevents the \"headers already sent\" error.</p>";
echo "</div>";

echo "</div></div>";

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'><i class='fas fa-test-tube me-2'></i>Test the Fix</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<p>Try accessing these pages to verify no more header errors:</p>";

$testPages = [
    'property.php?id=1' => 'Property Details',
    'my_properties.php' => 'My Properties (requires seller login)',
    'inbox.php' => 'Inbox (requires seller login)',
    'profile.php' => 'Profile (requires login)',
    'pay.php?property_id=1' => 'Payment Page (requires buyer login)'
];

echo "<div class='row'>";
foreach ($testPages as $url => $title) {
    echo "<div class='col-md-6 mb-2'>";
    echo "<a href='$url' class='btn btn-outline-primary btn-sm w-100' target='_blank'>";
    echo "<i class='fas fa-external-link-alt me-2'></i>$title";
    echo "</a>";
    echo "</div>";
}
echo "</div>";

echo "<div class='alert alert-success mt-3'>";
echo "<h5><i class='fas fa-check-circle me-2'></i>Expected Result:</h5>";
echo "<ul class='mb-0'>";
echo "<li>No \"Warning: Cannot modify header information\" errors</li>";
echo "<li>Proper redirects to login page when not authenticated</li>";
echo "<li>Clean page loading without PHP warnings</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

echo "<div class='text-center'>";
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>✅ HEADER ERRORS FIXED!</h4>";
echo "<p class='mb-0'>All files now handle redirects properly before outputting HTML.</p>";
echo "</div>";

echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='property.php?id=1' class='btn btn-primary'><i class='fas fa-home me-2'></i>Test Property Page</a>";
echo "<a href='index.php' class='btn btn-success'><i class='fas fa-home me-2'></i>Go to Homepage</a>";
echo "<a href='login.php' class='btn btn-info'><i class='fas fa-sign-in-alt me-2'></i>Login Page</a>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
