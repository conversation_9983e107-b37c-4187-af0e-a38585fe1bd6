<?php
// Quick Database Fix Script
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Database Fix</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 TX Properties Database Fix</h1>";
echo "<p>This script will add the missing columns to your database.</p>";

// SQL commands to add missing columns
$sql_commands = [
    // Add local_area column to properties table
    "ALTER TABLE properties ADD COLUMN local_area VARCHAR(100) AFTER location",

    // Add property features columns
    "ALTER TABLE properties ADD COLUMN has_electricity BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_gym BOOLEAN DEFAULT FALSE", 
    "ALTER TABLE properties ADD COLUMN has_air_condition BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_security BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_pool BOOLEAN DEFAULT FALSE",

    // Update property_type ENUM to include new types
    "ALTER TABLE properties MODIFY COLUMN property_type ENUM('Land', 'House', 'Apartment', 'Commercial', 'Office', 'Office Space', 'Store/Shop')"
];

$success_count = 0;
$error_count = 0;

// Execute each SQL command
foreach ($sql_commands as $index => $query) {
    echo "<div style='border:1px solid #ddd; padding:15px; margin:10px 0; border-radius:5px;'>";
    echo "<h3>Step " . ($index + 1) . "</h3>";
    echo "<p><strong>Executing:</strong> <code>" . htmlspecialchars($query) . "</code></p>";
    
    try {
        if ($conn->query($query)) {
            echo "<p class='success'>✅ Success!</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ Error: " . $conn->error . "</p>";
            $error_count++;
        }
    } catch (mysqli_sql_exception $e) {
        // Check if error is because column already exists
        if (strpos($e->getMessage(), "Duplicate column name") !== false || 
            strpos($e->getMessage(), "duplicate column name") !== false) {
            echo "<p class='info'>ℹ️ Column already exists, skipping.</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
            $error_count++;
        }
    }
    echo "</div>";
}

echo "<div style='background:#f0f8ff; padding:20px; border-radius:10px; margin:20px 0;'>";
echo "<h2>📊 Summary</h2>";
echo "<p><strong>Successful operations:</strong> $success_count</p>";
echo "<p><strong>Errors:</strong> $error_count</p>";

if ($error_count == 0) {
    echo "<p class='success'><strong>🎉 All database updates completed successfully!</strong></p>";
    echo "<p>You can now use the Add Property page without errors.</p>";
} else {
    echo "<p class='error'><strong>⚠️ Some errors occurred. Please check the details above.</strong></p>";
}
echo "</div>";

echo "<div style='text-align:center; margin:30px 0;'>";
echo "<a href='add_property.php' style='background:#007bff; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Go to Add Property</a>";
echo "<a href='index.php' style='background:#28a745; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Go to Homepage</a>";
echo "</div>";

// Close connection
$conn->close();

echo "</body></html>";
?>
