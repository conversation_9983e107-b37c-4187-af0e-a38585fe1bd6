<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in and is a seller
require_seller_login('seller_dashboard.php');

// Get seller information
$sellerId = $_SESSION['user_id'];
$sellerName = $_SESSION['username'];

// Get seller's properties
$sql = "SELECT COUNT(*) as total_properties FROM properties WHERE seller_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $sellerId);
$stmt->execute();
$result = $stmt->get_result();
$totalProperties = $result->fetch_assoc()['total_properties'];

// Get properties by status
$sql = "SELECT status, COUNT(*) as count FROM properties WHERE seller_id = ? GROUP BY status";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $sellerId);
$stmt->execute();
$result = $stmt->get_result();
$propertiesByStatus = [];
while ($row = $result->fetch_assoc()) {
    $propertiesByStatus[$row['status']] = $row['count'];
}

// Get total views of seller's properties (demo purpose - random number)
$totalViews = rand(50, 500);

// Get seller's earnings (demo purpose - random amount)
$totalEarnings = rand(1000000, 10000000);

// Get recent messages
$sql = "SELECT m.*, p.title as property_title, u.username as sender_name 
        FROM messages m 
        LEFT JOIN properties p ON m.property_id = p.id 
        LEFT JOIN users u ON m.sender_id = u.id 
        WHERE m.receiver_id = ? 
        ORDER BY m.created_at DESC 
        LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $sellerId);
$stmt->execute();
$result = $stmt->get_result();
$recentMessages = [];
while ($row = $result->fetch_assoc()) {
    $recentMessages[] = $row;
}

// Get recent properties
$sql = "SELECT * FROM properties WHERE seller_id = ? ORDER BY created_at DESC LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $sellerId);
$stmt->execute();
$result = $stmt->get_result();
$recentProperties = [];
while ($row = $result->fetch_assoc()) {
    $recentProperties[] = $row;
}

// Handle property deletion by seller
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    // Only allow delete if property belongs to this seller
    $sql = "SELECT id FROM properties WHERE id = ? AND seller_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ii', $id, $_SESSION['user_id']);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows > 0) {
        // Delete related images, payments, messages if needed
        $conn->query("DELETE FROM payments WHERE property_id = $id");
        $conn->query("DELETE FROM messages WHERE property_id = $id");
        $conn->query("DELETE FROM property_images WHERE property_id = $id");
        $conn->query("DELETE FROM properties WHERE id = $id");
        echo '<div class="alert alert-success">Property deleted.</div>';
    }
}
?>

<!-- Seller Dashboard Page -->
<div class="container my-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="h3 mb-2">Welcome, <?php echo $sellerName; ?>!</h1>
                            <p class="mb-0">Manage your property listings, view statistics, and handle inquiries from your dashboard.</p>
                        </div>
                        <div class="col-md-4 text-md-end mt-3 mt-md-0">
                            <a href="add_property.php" class="btn btn-light me-2">
                                <i class="fas fa-plus-circle me-2"></i> Add Property
                            </a>
                            <a href="inbox.php" class="btn btn-outline-light">
                                <i class="fas fa-envelope me-2"></i> Inbox
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 text-primary mb-2">
                        <i class="fas fa-home"></i>
                    </div>
                    <h5 class="card-title"><?php echo $totalProperties; ?></h5>
                    <p class="card-text text-muted">Total Properties</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 text-success mb-2">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h5 class="card-title"><?php echo $totalViews; ?></h5>
                    <p class="card-text text-muted">Total Views</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 text-info mb-2">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h5 class="card-title"><?php echo count($recentMessages); ?></h5>
                    <p class="card-text text-muted">New Messages</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-4 text-warning mb-2">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h5 class="card-title">TZS <?php echo number_format($totalEarnings, 0, '.', ','); ?></h5>
                    <p class="card-text text-muted">Total Earnings</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Properties by Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Properties by Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="me-3">
                                    <span class="badge bg-success p-2"><i class="fas fa-check fa-lg"></i></span>
                                </div>
                                <div>
                                    <h6 class="mb-0">Available</h6>
                                    <p class="mb-0 text-muted"><?php echo isset($propertiesByStatus['Available']) ? $propertiesByStatus['Available'] : 0; ?> properties</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3 mb-md-0">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="me-3">
                                    <span class="badge bg-danger p-2"><i class="fas fa-times fa-lg"></i></span>
                                </div>
                                <div>
                                    <h6 class="mb-0">Sold</h6>
                                    <p class="mb-0 text-muted"><?php echo isset($propertiesByStatus['Sold']) ? $propertiesByStatus['Sold'] : 0; ?> properties</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="me-3">
                                    <span class="badge bg-warning p-2"><i class="fas fa-clock fa-lg"></i></span>
                                </div>
                                <div>
                                    <h6 class="mb-0">Reserved</h6>
                                    <p class="mb-0 text-muted"><?php echo isset($propertiesByStatus['Reserved']) ? $propertiesByStatus['Reserved'] : 0; ?> properties</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Properties and Messages -->
    <div class="row">
        <!-- Recent Properties -->
        <div class="col-lg-7 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Properties</h5>
                    <a href="my_properties.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentProperties)): ?>
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-home fa-3x text-muted"></i>
                            </div>
                            <p class="mb-0">You haven't added any properties yet.</p>
                            <a href="add_property.php" class="btn btn-primary mt-3">Add Your First Property</a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Listing Type</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentProperties as $property): ?>
                                        <tr>
                                            <td>
                                                <a href="property.php?id=<?php echo $property['id']; ?>" class="text-decoration-none">
                                                    <?php echo $property['title']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo formatPrice($property['price'], $property['listing_type']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $property['status'] == 'Available' ? 'success' : ($property['status'] == 'Sold' ? 'danger' : 'secondary'); ?>"> <?php echo $property['status']; ?> </span>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $property['listing_type'] == 'Sale' ? 'badge-sale' : 'badge-rent'; ?>"> <?php echo $property['listing_type']; ?> </span>
                                            </td>
                                            <td>
                                                <a href="seller_edit_property.php?id=<?php echo $property['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                <a href="seller_dashboard.php?delete=<?php echo $property['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this property?');">Delete</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Messages -->
        <div class="col-lg-5 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Messages</h5>
                    <a href="inbox.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentMessages)): ?>
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-envelope fa-3x text-muted"></i>
                            </div>
                            <p class="mb-0">You don't have any messages yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group">
                            <?php foreach ($recentMessages as $message): ?>
                                <a href="view_message.php?id=<?php echo $message['id']; ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo $message['subject']; ?></h6>
                                        <small class="text-muted"><?php echo date('M d', strtotime($message['created_at'])); ?></small>
                                    </div>
                                    <p class="mb-1 text-truncate"><?php echo substr($message['message'], 0, 100); ?></p>
                                    <small>
                                        From: <?php echo $message['sender_name']; ?>
                                        <?php if (!empty($message['property_title'])): ?>
                                            about <?php echo $message['property_title']; ?>
                                        <?php endif; ?>
                                    </small>
                                    <?php if (!$message['read_status']): ?>
                                        <span class="float-end badge bg-primary">New</span>
                                    <?php endif; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3 mb-md-0">
                            <a href="add_property.php" class="btn btn-primary d-block p-3">
                                <i class="fas fa-plus-circle fa-lg mb-2 d-block"></i>
                                Add New Property
                            </a>
                        </div>
                        <div class="col-md-3 mb-3 mb-md-0">
                            <a href="my_properties.php" class="btn btn-outline-primary d-block p-3">
                                <i class="fas fa-list fa-lg mb-2 d-block"></i>
                                Manage Properties
                            </a>
                        </div>
                        <div class="col-md-3 mb-3 mb-md-0">
                            <a href="inbox.php" class="btn btn-outline-primary d-block p-3">
                                <i class="fas fa-envelope fa-lg mb-2 d-block"></i>
                                Check Messages
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="profile.php" class="btn btn-outline-primary d-block p-3">
                                <i class="fas fa-user-edit fa-lg mb-2 d-block"></i>
                                Update Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?> 