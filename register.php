<?php
// Include header and required files
require_once 'includes/header.php';

// Initialize variables
$username = '';
$email = '';
$fullName = '';
$phone = '';
$error = '';
$success = '';

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $passwordConfirm = $_POST['password_confirm'];
    $fullName = sanitize($_POST['full_name']);
    $phone = sanitize($_POST['phone']);
    $role = sanitize($_POST['role']);

    // Validate inputs
    $errors = [];
    
    if (empty($username)) {
        $errors[] = "Username is required.";
    } elseif (strlen($username) < 3 || strlen($username) > 50) {
        $errors[] = "Username must be between 3 and 50 characters.";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Please enter a valid email address.";
    }
    
    if (empty($password)) {
        $errors[] = "Password is required.";
    } elseif (strlen($password) < 6) {
        $errors[] = "Password must be at least 6 characters.";
    }
    
    if ($password !== $passwordConfirm) {
        $errors[] = "Passwords do not match.";
    }
    
    if (empty($role)) {
        $errors[] = "Please select a role.";
    }
    
    // Check if username or email already exists
    $sql = "SELECT * FROM users WHERE username = ? OR email = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $username, $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        if ($user['username'] === $username) {
            $errors[] = "Username already exists. Please choose another.";
        }
        if ($user['email'] === $email) {
            $errors[] = "Email already exists. Please use another email or login.";
        }
    }
    
    // If no errors, create the user
    if (empty($errors)) {
        // In a real application, hash the password: $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        // For demo purposes, we're using plain text (not recommended for production)
        $hashedPassword = $password;
        
        $sql = "INSERT INTO users (username, email, password, role, full_name, phone) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssss", $username, $email, $hashedPassword, $role, $fullName, $phone);
        
        if ($stmt->execute()) {
            $success = "Registration successful! You can now login.";
            
            // Clear form data
            $username = '';
            $email = '';
            $fullName = '';
            $phone = '';
        } else {
            $error = "Registration failed. Please try again later.";
        }
    } else {
        $error = implode("<br>", $errors);
    }
}
?>

<!-- Add auth page styling -->
<style>
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    min-height: 100vh;
}
</style>

<!-- Registration Page Content -->
<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100 py-5">
    <div class="row w-100 justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0" style="border-radius: 15px; overflow: hidden;">
                <div class="card-header text-center py-4" style="background: linear-gradient(135deg, #2c5530 0%, #34495e 100%); border: none;">
                    <h4 class="mb-0 text-white fw-bold">Create a New Account</h4>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    
                    <form action="register.php" method="post" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label fw-semibold text-dark">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-lg" id="username" name="username"
                                       value="<?php echo htmlspecialchars($username); ?>" required
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <div class="invalid-feedback">Please choose a username.</div>
                                <small class="form-text text-muted">3-50 characters long.</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label fw-semibold text-dark">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control form-control-lg" id="email" name="email"
                                       value="<?php echo htmlspecialchars($email); ?>" required
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <div class="invalid-feedback">Please enter a valid email address.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label fw-semibold text-dark">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control form-control-lg" id="password" name="password" required
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <div class="invalid-feedback">Please enter a password.</div>
                                <small class="form-text text-muted">At least 6 characters long.</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="password_confirm" class="form-label fw-semibold text-dark">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control form-control-lg" id="password_confirm" name="password_confirm" required
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <div class="invalid-feedback">Please confirm your password.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label fw-semibold text-dark">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-lg" id="full_name" name="full_name"
                                       value="<?php echo htmlspecialchars($fullName); ?>" required
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <div class="invalid-feedback">Please enter your full name.</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label fw-semibold text-dark">Phone Number</label>
                                <input type="tel" class="form-control form-control-lg" id="phone" name="phone"
                                       value="<?php echo htmlspecialchars($phone); ?>"
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <small class="form-text text-muted">Optional</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label fw-semibold text-dark">I want to <span class="text-danger">*</span></label>
                            <select class="form-select form-select-lg" id="role" name="role" required
                                    style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px;">
                                <option value="">Choose your role</option>
                                <option value="buyer">Buy Properties</option>
                                <option value="seller">Sell Properties</option>
                            </select>
                            <div class="invalid-feedback">Please select your role.</div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="terms.php" target="_blank" class="text-decoration-none" style="color: #2c5530;">Terms of Service</a>
                                and <a href="privacy.php" target="_blank" class="text-decoration-none" style="color: #2c5530;">Privacy Policy</a>
                            </label>
                            <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-lg text-white fw-bold"
                                    style="background: linear-gradient(135deg, #2c5530 0%, #34495e 100%);
                                           border: none; border-radius: 10px; padding: 12px;
                                           transition: all 0.3s ease;">
                                <i class="fas fa-user-plus me-2"></i> Register
                            </button>
                        </div>
                    </form>

                    <div class="mt-4 text-center">
                        <p class="mb-0 text-muted">Already have an account?
                            <a href="login.php" class="text-decoration-none fw-semibold"
                               style="color: #2c5530;">Login here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Form JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth focus transitions
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'all 0.3s ease';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add button hover effects
    const btn = document.querySelector('.btn');
    if (btn) {
        btn.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(135deg, #1e3a8a 0%, #2c5530 100%)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.background = 'linear-gradient(135deg, #2c5530 0%, #34495e 100%)';
        });
    }

    // Password confirmation validation
    const password = document.getElementById('password');
    const passwordConfirm = document.getElementById('password_confirm');

    if (password && passwordConfirm) {
        passwordConfirm.addEventListener('input', function() {
            if (password.value !== passwordConfirm.value) {
                passwordConfirm.setCustomValidity('Passwords do not match');
            } else {
                passwordConfirm.setCustomValidity('');
            }
        });
    }
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?> 