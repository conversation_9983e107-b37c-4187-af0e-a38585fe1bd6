<?php
/**
 * Enhanced Form Validation Functions for TX Properties
 * Implements strict validation rules for all form inputs
 */

/**
 * Validate Name/Full Name - Only alphabetic characters and spaces
 * @param string $name The name to validate
 * @param int $minLength Minimum length (default: 2)
 * @param int $maxLength Maximum length (default: 100)
 * @return array ['valid' => bool, 'message' => string]
 */
function validateName($name, $minLength = 2, $maxLength = 100) {
    $name = trim($name);
    
    if (empty($name)) {
        return ['valid' => false, 'message' => 'Name is required.'];
    }
    
    if (strlen($name) < $minLength) {
        return ['valid' => false, 'message' => "Name must be at least {$minLength} characters long."];
    }
    
    if (strlen($name) > $maxLength) {
        return ['valid' => false, 'message' => "Name must not exceed {$maxLength} characters."];
    }
    
    // Allow only alphabetic characters (a-z, A-Z) and spaces - NO numbers or special characters
    if (!preg_match("/^[a-zA-Z\s]+$/", $name)) {
        return ['valid' => false, 'message' => 'Name can only contain alphabetic characters (a-z, A-Z) and spaces. No numbers or special characters allowed.'];
    }
    
    // Check for multiple consecutive spaces
    if (preg_match("/\s{2,}/", $name)) {
        return ['valid' => false, 'message' => 'Name cannot contain multiple consecutive spaces.'];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * Validate Username - Letters, numbers, underscores, and dots only
 * @param string $username The username to validate
 * @param int $minLength Minimum length (default: 3)
 * @param int $maxLength Maximum length (default: 30)
 * @return array ['valid' => bool, 'message' => string]
 */
function validateUsername($username, $minLength = 3, $maxLength = 30) {
    $username = trim($username);
    
    if (empty($username)) {
        return ['valid' => false, 'message' => 'Username is required.'];
    }
    
    if (strlen($username) < $minLength) {
        return ['valid' => false, 'message' => "Username must be at least {$minLength} characters long."];
    }
    
    if (strlen($username) > $maxLength) {
        return ['valid' => false, 'message' => "Username must not exceed {$maxLength} characters."];
    }
    
    // Allow only letters, numbers, underscores, and dots - NO spaces or special characters like @, #, etc.
    if (!preg_match("/^[a-zA-Z0-9._]+$/", $username)) {
        return ['valid' => false, 'message' => 'Username can only contain letters, numbers, underscores, and dots. No spaces or special characters like @, #, etc.'];
    }
    
    // Must start with a letter
    if (!preg_match("/^[a-zA-Z]/", $username)) {
        return ['valid' => false, 'message' => 'Username must start with a letter.'];
    }
    
    // Cannot start or end with dots or underscores
    if (preg_match("/^[._]|[._]$/", $username)) {
        return ['valid' => false, 'message' => 'Username cannot start or end with dots or underscores.'];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * Validate Email Address
 * @param string $email The email to validate
 * @return array ['valid' => bool, 'message' => string]
 */
function validateEmail($email) {
    $email = trim($email);
    
    if (empty($email)) {
        return ['valid' => false, 'message' => 'Email address is required.'];
    }
    
    if (strlen($email) > 254) {
        return ['valid' => false, 'message' => 'Email address is too long.'];
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['valid' => false, 'message' => 'Please enter a valid email address.'];
    }
    
    // Additional checks for common issues
    if (substr_count($email, '@') !== 1) {
        return ['valid' => false, 'message' => 'Email address must contain exactly one @ symbol.'];
    }
    
    $parts = explode('@', $email);
    if (strlen($parts[0]) > 64) {
        return ['valid' => false, 'message' => 'Email local part is too long.'];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * Validate Phone Number - Digits with optional country code
 * @param string $phone The phone number to validate
 * @param bool $required Whether phone is required (default: false)
 * @return array ['valid' => bool, 'message' => string]
 */
function validatePhone($phone, $required = false) {
    $phone = trim($phone);
    
    if (empty($phone)) {
        if ($required) {
            return ['valid' => false, 'message' => 'Phone number is required.'];
        }
        return ['valid' => true, 'message' => ''];
    }
    
    // Remove all non-digit characters except + at the beginning
    $cleanPhone = preg_replace('/[^\d+]/', '', $phone);
    
    // Check if it starts with + (country code)
    $hasCountryCode = strpos($cleanPhone, '+') === 0;
    
    if ($hasCountryCode) {
        // Remove the + for length checking
        $digits = substr($cleanPhone, 1);
        if (strlen($digits) < 10 || strlen($digits) > 15) {
            return ['valid' => false, 'message' => 'Phone number with country code must be 10-15 digits long.'];
        }
    } else {
        // Local number without country code
        if (strlen($cleanPhone) < 10 || strlen($cleanPhone) > 15) {
            return ['valid' => false, 'message' => 'Phone number must be 10-15 digits long.'];
        }
    }
    
    // Validate format: +XXX XXXXXXXXXX or XXXXXXXXXX
    $pattern = $hasCountryCode ? '/^\+\d{1,3}\d{7,12}$/' : '/^\d{10,15}$/';
    if (!preg_match($pattern, $cleanPhone)) {
        return ['valid' => false, 'message' => 'Please enter a valid phone number format.'];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * Validate Property Title - Letters, numbers, and basic punctuation
 * @param string $title The property title to validate
 * @param int $minLength Minimum length (default: 5)
 * @param int $maxLength Maximum length (default: 200)
 * @return array ['valid' => bool, 'message' => string]
 */
function validatePropertyTitle($title, $minLength = 5, $maxLength = 200) {
    $title = trim($title);
    
    if (empty($title)) {
        return ['valid' => false, 'message' => 'Property title is required.'];
    }
    
    if (strlen($title) < $minLength) {
        return ['valid' => false, 'message' => "Property title must be at least {$minLength} characters long."];
    }
    
    if (strlen($title) > $maxLength) {
        return ['valid' => false, 'message' => "Property title must not exceed {$maxLength} characters."];
    }
    
    // Accept letters, numbers, and basic punctuation (commas, periods) - Disallow symbols like < > {}
    if (!preg_match("/^[a-zA-Z0-9\s.,!?\-'\"()&]+$/", $title)) {
        return ['valid' => false, 'message' => 'Property title contains invalid characters. Only letters, numbers, spaces, and basic punctuation (commas, periods) are allowed. Symbols like < > {} are not permitted.'];
    }

    // Specifically check for disallowed symbols
    if (preg_match("/[<>{}[\]\\\\|`~@#$%^*+=]/", $title)) {
        return ['valid' => false, 'message' => 'Property title cannot contain symbols like < > {} [ ] \\ | ` ~ @ # $ % ^ * + ='];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * Validate Property Price - Numeric value with optional formatting
 * @param string $price The price to validate
 * @param float $minPrice Minimum price (default: 0)
 * @param float $maxPrice Maximum price (default: 999999999)
 * @return array ['valid' => bool, 'message' => string, 'cleaned_price' => float]
 */
function validatePropertyPrice($price, $minPrice = 0, $maxPrice = 999999999) {
    $price = trim($price);
    
    if (empty($price)) {
        return ['valid' => false, 'message' => 'Property price is required.', 'cleaned_price' => 0];
    }
    
    // Remove currency symbols and formatting
    $cleanPrice = preg_replace('/[^\d.,]/', '', $price);
    
    // Handle different decimal formats (23,000.50 or 23.000,50)
    if (preg_match('/^\d{1,3}(,\d{3})*(\.\d{2})?$/', $cleanPrice)) {
        // US format: 23,000.50
        $numericPrice = (float) str_replace(',', '', $cleanPrice);
    } elseif (preg_match('/^\d{1,3}(\.\d{3})*(,\d{2})?$/', $cleanPrice)) {
        // European format: 23.000,50
        $cleanPrice = str_replace('.', '', $cleanPrice);
        $numericPrice = (float) str_replace(',', '.', $cleanPrice);
    } else {
        // Simple number
        $numericPrice = (float) $cleanPrice;
    }
    
    if (!is_numeric($numericPrice) || $numericPrice < 0) {
        return ['valid' => false, 'message' => 'Please enter a valid price.', 'cleaned_price' => 0];
    }
    
    if ($numericPrice < $minPrice) {
        return ['valid' => false, 'message' => "Price must be at least " . number_format($minPrice) . ".", 'cleaned_price' => $numericPrice];
    }
    
    if ($numericPrice > $maxPrice) {
        return ['valid' => false, 'message' => "Price cannot exceed " . number_format($maxPrice) . ".", 'cleaned_price' => $numericPrice];
    }
    
    return ['valid' => true, 'message' => '', 'cleaned_price' => $numericPrice];
}

/**
 * Validate Description - Longer text with sanitization
 * @param string $description The description to validate
 * @param int $minLength Minimum length (default: 10)
 * @param int $maxLength Maximum length (default: 5000)
 * @return array ['valid' => bool, 'message' => string, 'sanitized' => string]
 */
function validateDescription($description, $minLength = 10, $maxLength = 5000) {
    $description = trim($description);
    
    if (empty($description)) {
        return ['valid' => false, 'message' => 'Description is required.', 'sanitized' => ''];
    }
    
    if (strlen($description) < $minLength) {
        return ['valid' => false, 'message' => "Description must be at least {$minLength} characters long.", 'sanitized' => $description];
    }
    
    if (strlen($description) > $maxLength) {
        return ['valid' => false, 'message' => "Description must not exceed {$maxLength} characters.", 'sanitized' => $description];
    }
    
    // Sanitize to prevent script injections
    $sanitized = htmlspecialchars($description, ENT_QUOTES, 'UTF-8');
    
    // Remove potentially dangerous tags
    $sanitized = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $sanitized);
    $sanitized = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $sanitized);
    
    return ['valid' => true, 'message' => '', 'sanitized' => $sanitized];
}

/**
 * Validate Password
 * @param string $password The password to validate
 * @param int $minLength Minimum length (default: 8)
 * @return array ['valid' => bool, 'message' => string]
 */
function validatePassword($password, $minLength = 8) {
    if (empty($password)) {
        return ['valid' => false, 'message' => 'Password is required.'];
    }
    
    if (strlen($password) < $minLength) {
        return ['valid' => false, 'message' => "Password must be at least {$minLength} characters long."];
    }
    
    // Check for at least one uppercase letter
    if (!preg_match('/[A-Z]/', $password)) {
        return ['valid' => false, 'message' => 'Password must contain at least one uppercase letter.'];
    }
    
    // Check for at least one lowercase letter
    if (!preg_match('/[a-z]/', $password)) {
        return ['valid' => false, 'message' => 'Password must contain at least one lowercase letter.'];
    }
    
    // Check for at least one number
    if (!preg_match('/\d/', $password)) {
        return ['valid' => false, 'message' => 'Password must contain at least one number.'];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * Clean and format phone number for storage
 * @param string $phone The phone number to clean
 * @return string Cleaned phone number
 */
function cleanPhoneNumber($phone) {
    // Remove all non-digit characters except + at the beginning
    $cleaned = preg_replace('/[^\d+]/', '', trim($phone));
    return $cleaned;
}

/**
 * Capitalize words properly for names
 * @param string $name The name to capitalize
 * @return string Properly capitalized name
 */
function capitalizeWords($name) {
    // Convert to lowercase first, then capitalize each word
    $name = trim(strtolower($name));

    // Handle special cases for names
    $name = ucwords($name);

    // Handle apostrophes (O'Connor, D'Angelo, etc.)
    $name = preg_replace_callback("/\b(\w+)'(\w+)\b/", function($matches) {
        return ucfirst($matches[1]) . "'" . ucfirst($matches[2]);
    }, $name);

    // Handle hyphens (Mary-Jane, etc.)
    $name = preg_replace_callback("/\b(\w+)-(\w+)\b/", function($matches) {
        return ucfirst($matches[1]) . "-" . ucfirst($matches[2]);
    }, $name);

    return $name;
}

/**
 * Sanitize input to prevent XSS attacks
 * @param string $input The input to sanitize
 * @return string Sanitized input
 */
function sanitizeValidationInput($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * Validate and sanitize all form inputs
 * @param array $data Array of form data
 * @param array $rules Array of validation rules
 * @return array ['valid' => bool, 'errors' => array, 'data' => array]
 */
function validateFormData($data, $rules) {
    $errors = [];
    $cleanData = [];

    foreach ($rules as $field => $rule) {
        $value = isset($data[$field]) ? $data[$field] : '';

        switch ($rule['type']) {
            case 'name':
                $validation = validateName($value, $rule['min'] ?? 2, $rule['max'] ?? 100);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = capitalizeWords($value);
                }
                break;

            case 'username':
                $validation = validateUsername($value, $rule['min'] ?? 3, $rule['max'] ?? 30);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = strtolower(trim($value));
                }
                break;

            case 'email':
                $validation = validateEmail($value);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = strtolower(trim($value));
                }
                break;

            case 'phone':
                $validation = validatePhone($value, $rule['required'] ?? false);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = cleanPhoneNumber($value);
                }
                break;

            case 'property_title':
                $validation = validatePropertyTitle($value, $rule['min'] ?? 5, $rule['max'] ?? 200);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = trim($value);
                }
                break;

            case 'price':
                $validation = validatePropertyPrice($value, $rule['min'] ?? 0, $rule['max'] ?? 999999999);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = $validation['cleaned_price'];
                }
                break;

            case 'description':
                $validation = validateDescription($value, $rule['min'] ?? 10, $rule['max'] ?? 5000);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = $validation['sanitized'];
                }
                break;

            case 'password':
                $validation = validatePassword($value, $rule['min'] ?? 8);
                if (!$validation['valid']) {
                    $errors[$field] = $validation['message'];
                } else {
                    $cleanData[$field] = $value; // Don't modify password
                }
                break;

            default:
                $cleanData[$field] = sanitizeValidationInput($value);
                break;
        }
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'data' => $cleanData
    ];
}
?>
