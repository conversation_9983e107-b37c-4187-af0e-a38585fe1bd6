<?php
// Start output buffering to prevent "headers already sent" error
ob_start();

// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in and is a seller
require_seller_login('add_property.php');

// Initialize variables
$title = '';
$description = '';
$price = '';
$location = '';
$localArea = '';
$propertyType = '';
$listingType = 'Sale';
$bedrooms = '';
$bathrooms = '';
$area = '';
$latitude = '-6.776012'; // Default to Tanzania center
$longitude = '39.178326';
$error = '';
$success = '';

// Define Tanzania regions
$regions = [
    'Arusha',
    'Dar es Salaam',
    'Dodoma',
    'Mbeya',
    'Tanga',
    'Zanzibar'
];

// Get locations for dropdown
$sql = "SELECT DISTINCT location FROM properties ORDER BY location ASC";
$result = $conn->query($sql);
$locations = [];
while ($row = $result->fetch_assoc()) {
    $locations[] = $row['location'];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $price = sanitize($_POST['price']);
    $location = sanitize($_POST['location']);
    $localArea = isset($_POST['local_area']) ? sanitize($_POST['local_area']) : '';
    $propertyType = sanitize($_POST['property_type']);
    $listingType = sanitize($_POST['listing_type']);
    $bedrooms = !empty($_POST['bedrooms']) ? sanitize($_POST['bedrooms']) : NULL;
    $bathrooms = !empty($_POST['bathrooms']) ? sanitize($_POST['bathrooms']) : NULL;
    $area = !empty($_POST['area']) ? sanitize($_POST['area']) : NULL;
    $latitude = sanitize($_POST['latitude']);
    $longitude = sanitize($_POST['longitude']);
    // Property features
    $hasElectricity = isset($_POST['has_electricity']) ? 1 : 0;
    $hasGym = isset($_POST['has_gym']) ? 1 : 0;
    $hasAirCondition = isset($_POST['has_air_condition']) ? 1 : 0;
    $hasSecurity = isset($_POST['has_security']) ? 1 : 0;
    $hasPool = isset($_POST['has_pool']) ? 1 : 0;
    $sellerId = $_SESSION['user_id'];
    
    // Validate inputs
    $errors = [];
    
    if (empty($title)) {
        $errors[] = "Property title is required.";
    }
    
    if (empty($description)) {
        $errors[] = "Property description is required.";
    }
    
    if (empty($price)) {
        $errors[] = "Property price is required.";
    } elseif (!is_numeric($price) || $price <= 0) {
        $errors[] = "Price must be a positive number.";
    }
    
    if (empty($location)) {
        $errors[] = "Location is required.";
    }
    
    if (empty($propertyType)) {
        $errors[] = "Property type is required.";
    }
    
    if (empty($listingType)) {
        $errors[] = "Listing type is required.";
    }
    
    // If no errors, insert property
    if (empty($errors)) {
        // Verify seller ID exists in the database
        $sellerId = $_SESSION['user_id'];
        
        // Check if the seller ID exists in the users table
        $checkUserSql = "SELECT id FROM users WHERE id = ?";
        $checkUserStmt = $conn->prepare($checkUserSql);
        $checkUserStmt->bind_param("i", $sellerId);
        $checkUserStmt->execute();
        $checkUserResult = $checkUserStmt->get_result();
        
        if ($checkUserResult->num_rows === 0) {
            $errors[] = "Invalid seller ID. Please logout and login again.";
            $error = implode("<br>", $errors);
        } else {
            // Insert property
            $sql = "INSERT INTO properties (title, description, price, location, local_area, property_type, listing_type, 
                    bedrooms, bathrooms, area, seller_id, lat, lng, has_electricity, has_gym, has_air_condition, has_security, has_pool) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            
            // Convert NULL values to appropriate types for bind_param
            if ($bedrooms === NULL) $bedrooms = 0;
            if ($bathrooms === NULL) $bathrooms = 0;
            if ($area === NULL) $area = 0;
            
            // Make sure numeric values are properly typed
            $price = (float)$price;
            $bedrooms = (int)$bedrooms;
            $bathrooms = (int)$bathrooms;
            $area = (float)$area;
            $sellerId = (int)$sellerId;
            $latitude = (float)$latitude;
            $longitude = (float)$longitude;
            $hasElectricity = (int)$hasElectricity;
            $hasGym = (int)$hasGym;
            $hasAirCondition = (int)$hasAirCondition;
            $hasSecurity = (int)$hasSecurity;
            $hasPool = (int)$hasPool;
            
            // Count the number of parameters in the SQL query
            // There are 18 question marks in the VALUES clause
            // So we need 18 type specifiers and 18 variables
            
            // Bind parameters with correct types
            // s = string, d = double, i = integer
            $stmt->bind_param(
                "ssdssssiiidddiiiii", 
                $title,               // s - string
                $description,         // s - string
                $price,               // d - double
                $location,            // s - string
                $localArea,           // s - string
                $propertyType,        // s - string
                $listingType,         // s - string
                $bedrooms,            // i - integer
                $bathrooms,           // i - integer
                $area,                // d - double
                $sellerId,            // i - integer
                $latitude,            // d - double
                $longitude,           // d - double
                $hasElectricity,      // i - integer
                $hasGym,              // i - integer
                $hasAirCondition,     // i - integer
                $hasSecurity,         // i - integer
                $hasPool              // i - integer
            );
            
            if ($stmt->execute()) {
                $propertyId = $conn->insert_id;
                
                // Process images
                $uploadedImages = 0;
                
                if (!empty($_FILES['images']['name'][0])) {
                    // Create directory if it doesn't exist
                    $uploadDir = 'assets/images/properties/' . $propertyId . '/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }
                    
                    // Process each image
                    foreach ($_FILES['images']['tmp_name'] as $key => $tmp_name) {
                        if ($_FILES['images']['error'][$key] === 0) {
                            $fileName = basename($_FILES['images']['name'][$key]);
                            $fileExt = pathinfo($fileName, PATHINFO_EXTENSION);
                            $newFileName = 'property_' . $propertyId . '_' . ($uploadedImages + 1) . '.' . $fileExt;
                            $targetFile = $uploadDir . $newFileName;
                            
                            // Move file
                            if (move_uploaded_file($tmp_name, $targetFile)) {
                                // Insert image into database
                                $isPrimary = $uploadedImages === 0 ? 1 : 0; // First image is primary
                                $imagePath = $targetFile;
                                
                                $sql = "INSERT INTO property_images (property_id, image_path, is_primary) VALUES (?, ?, ?)";
                                $stmt = $conn->prepare($sql);
                                $stmt->bind_param("isi", $propertyId, $imagePath, $isPrimary);
                                $stmt->execute();
                                
                                $uploadedImages++;
                            }
                        }
                    }
                }
                
                // Set success message
                $success = "Property added successfully!";
                
                // Redirect to seller dashboard
                header("Location: seller_dashboard.php?success=" . urlencode("Property added successfully!"));
                exit;
            } else {
                $error = "Error adding property. Please try again.";
            }
        }
    } else {
        $error = implode("<br>", $errors);
    }
}
?>

<!-- Add Property Page -->
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">Add New Property</h1>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <form action="add_property.php" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Property Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">Property Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo $title; ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a title for the property.
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="5" required><?php echo $description; ?></textarea>
                                <div class="invalid-feedback">
                                    Please provide a description of the property.
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price (TZS) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" value="<?php echo $price; ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a price.
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="property_type" class="form-label">Property Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="property_type" name="property_type" required>
                                    <option value="">Select Type</option>
                                    <option value="Land" <?php echo $propertyType === 'Land' ? 'selected' : ''; ?>>Land</option>
                                    <option value="House" <?php echo $propertyType === 'House' ? 'selected' : ''; ?>>House</option>
                                    <option value="Apartment" <?php echo $propertyType === 'Apartment' ? 'selected' : ''; ?>>Apartment</option>
                                    <option value="Commercial" <?php echo $propertyType === 'Commercial' ? 'selected' : ''; ?>>Commercial</option>
                                    <option value="Office" <?php echo $propertyType === 'Office' ? 'selected' : ''; ?>>Office</option>
                                    <option value="Office Space" <?php echo $propertyType === 'Office Space' ? 'selected' : ''; ?>>Office Space</option>
                                    <option value="Store/Shop" <?php echo $propertyType === 'Store/Shop' ? 'selected' : ''; ?>>Store/Shop</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a property type.
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Listing Type <span class="text-danger">*</span></label>
                                <div class="d-flex">
                                    <div class="form-check me-3">
                                        <input class="form-check-input" type="radio" name="listing_type" id="listing_sale" value="Sale" <?php echo $listingType === 'Sale' ? 'checked' : ''; ?> required>
                                        <label class="form-check-label" for="listing_sale">
                                            For Sale
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="listing_type" id="listing_rent" value="Rent" <?php echo $listingType === 'Rent' ? 'checked' : ''; ?> required>
                                        <label class="form-check-label" for="listing_rent">
                                            For Rent
                                        </label>
                                    </div>
                                </div>
                                <div class="invalid-feedback">
                                    Please select a listing type.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Property Features</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="bedrooms" class="form-label">Bedrooms</label>
                                <input type="number" class="form-control" id="bedrooms" name="bedrooms" value="<?php echo $bedrooms; ?>">
                                <small class="form-text text-muted">Leave empty for land properties.</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="bathrooms" class="form-label">Bathrooms</label>
                                <input type="number" class="form-control" id="bathrooms" name="bathrooms" value="<?php echo $bathrooms; ?>">
                                <small class="form-text text-muted">Leave empty for land properties.</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="area" class="form-label">Area (sqm)</label>
                                <input type="number" step="0.01" class="form-control" id="area" name="area" value="<?php echo $area; ?>">
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12 mb-2">
                                <label class="form-label">Additional Features</label>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_electricity" name="has_electricity" value="1" <?php echo isset($hasElectricity) && $hasElectricity ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_electricity">
                                        <i class="fas fa-bolt text-warning me-2"></i> Electricity
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_gym" name="has_gym" value="1" <?php echo isset($hasGym) && $hasGym ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_gym">
                                        <i class="fas fa-dumbbell text-primary me-2"></i> Gym
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_air_condition" name="has_air_condition" value="1" <?php echo isset($hasAirCondition) && $hasAirCondition ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_air_condition">
                                        <i class="fas fa-snowflake text-info me-2"></i> Air Condition
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_security" name="has_security" value="1" <?php echo isset($hasSecurity) && $hasSecurity ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_security">
                                        <i class="fas fa-shield-alt text-success me-2"></i> Security
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_pool" name="has_pool" value="1" <?php echo isset($hasPool) && $hasPool ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_pool">
                                        <i class="fas fa-swimming-pool text-primary me-2"></i> Swimming Pool
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Location Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4 mb-3">
                                <label for="location" class="form-label">Region <span class="text-danger">*</span></label>
                                <select class="form-select" id="location" name="location" required>
                                    <option value="">Select Region</option>
                                    <?php foreach ($regions as $region): ?>
                                        <option value="<?php echo $region; ?>" <?php echo $location === $region ? 'selected' : ''; ?>><?php echo $region; ?></option>
                                    <?php endforeach; ?>
                                    <option value="other">Other (Specify)</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a region.
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="local_area" class="form-label">Local Area</label>
                                <input type="text" class="form-control" id="local_area" name="local_area" value="<?php echo $localArea; ?>" placeholder="e.g. Kikuyu, Mwananyamala">
                                <small class="form-text text-muted">Specific area within the region</small>
                            </div>
                            
                            <div class="col-md-4 mb-3" id="otherLocationContainer" style="display: none;">
                                <label for="other_location" class="form-label">Specify Region <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="other_location" name="other_location">
                                <div class="invalid-feedback">
                                    Please specify the region.
                                </div>
                            </div>
                        </div>
                        
                        <!-- Map for selecting property location -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Property Map Location <span class="text-danger">*</span></label>
                                <p class="text-muted small">Click on the map to set the exact property location or adjust the coordinates manually below.</p>
                                <div id="property-location-map" style="height: 400px;" class="rounded mb-3"></div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="latitude" name="latitude" value="<?php echo $latitude; ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="longitude" name="longitude" value="<?php echo $longitude; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Property Images</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="images" class="form-label">Upload Images <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="images" name="images[]" accept="image/*" multiple required>
                                <div class="invalid-feedback">
                                    Please upload at least one image.
                                </div>
                                <small class="form-text text-muted">
                                    You can upload multiple images. The first image will be used as the main image.
                                    Max file size: 5MB per image. Supported formats: JPG, PNG, GIF.
                                </small>
                            </div>
                            
                            <div class="col-md-12">
                                <div class="row" id="image_preview_container"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                    <a href="seller_dashboard.php" class="btn btn-outline-secondary me-md-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> Add Property
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Example starter JavaScript for disabling form submissions if there are invalid fields
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            // Fetch all the forms we want to apply custom Bootstrap validation styles to
            var forms = document.querySelectorAll('.needs-validation');
            
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms)
                .forEach(function(form) {
                    form.addEventListener('submit', function(event) {
                        if (!form.checkValidity()) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        
                        form.classList.add('was-validated');
                    }, false);
                });
        }, false);
    })();
    
    // Location dropdown handling
    document.getElementById('location').addEventListener('change', function() {
        const otherLocationContainer = document.getElementById('otherLocationContainer');
        const otherLocationInput = document.getElementById('other_location');
        
        if (this.value === 'other') {
            otherLocationContainer.style.display = 'block';
            otherLocationInput.setAttribute('required', '');
        } else {
            otherLocationContainer.style.display = 'none';
            otherLocationInput.removeAttribute('required');
        }
    });

    // Initialize Leaflet map for property location
    document.addEventListener('DOMContentLoaded', function() {
        // Default to Tanzania center if no coordinates
        let lat = parseFloat(document.getElementById('latitude').value) || -6.776012;
        let lng = parseFloat(document.getElementById('longitude').value) || 39.178326;
        
        // Initialize the map
        const propertyMap = L.map('property-location-map').setView([lat, lng], 7);
        
        // Add the OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(propertyMap);
        
        // Add a marker at the initial position
        let marker = L.marker([lat, lng], {
            draggable: true
        }).addTo(propertyMap);
        
        // Update lat/lng inputs when marker is dragged
        marker.on('dragend', function(event) {
            const position = marker.getLatLng();
            document.getElementById('latitude').value = position.lat.toFixed(6);
            document.getElementById('longitude').value = position.lng.toFixed(6);
        });
        
        // Allow clicking on map to move marker
        propertyMap.on('click', function(e) {
            const position = e.latlng;
            marker.setLatLng(position);
            document.getElementById('latitude').value = position.lat.toFixed(6);
            document.getElementById('longitude').value = position.lng.toFixed(6);
        });
        
        // Update marker when lat/lng inputs change
        document.getElementById('latitude').addEventListener('change', updateMarkerFromInputs);
        document.getElementById('longitude').addEventListener('change', updateMarkerFromInputs);
        
        function updateMarkerFromInputs() {
            const lat = parseFloat(document.getElementById('latitude').value);
            const lng = parseFloat(document.getElementById('longitude').value);
            
            if (!isNaN(lat) && !isNaN(lng)) {
                marker.setLatLng([lat, lng]);
                propertyMap.setView([lat, lng], propertyMap.getZoom());
            }
        }

        // Automatically focus map on the selected location if available
        document.getElementById('location').addEventListener('change', function() {
            if (this.value !== '' && this.value !== 'other') {
                // You could implement a geocoding service here to get coordinates for the selected location
                // For demonstration, we'll just keep the current view
                propertyMap.setView(marker.getLatLng(), 12);
            }
        });
        
        // Fix map display issues that might occur due to hidden containers
        setTimeout(function() {
            propertyMap.invalidateSize();
        }, 500);
    });

    document.addEventListener('DOMContentLoaded', function() {
        const input = document.getElementById('images');
        const previewContainer = document.getElementById('image_preview_container');
        if (input && previewContainer) {
            input.addEventListener('change', function() {
                previewContainer.innerHTML = '';
                const files = input.files;
                if (files) {
                    Array.from(files).forEach(file => {
                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const col = document.createElement('div');
                                col.className = 'col-4 col-md-3 mb-2';
                                col.innerHTML = `<img src='${e.target.result}' class='img-thumbnail' style='width:100%;height:100px;object-fit:cover;'>`;
                                previewContainer.appendChild(col);
                            };
                            reader.readAsDataURL(file);
                        }
                    });
                }
            });
        }
    });
</script>

<?php
// Include footer
require_once 'includes/footer.php';

// End output buffering and send content to browser
ob_end_flush();
?> 