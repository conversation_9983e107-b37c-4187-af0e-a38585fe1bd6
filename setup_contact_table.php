<?php
// Simple database connection
$host = 'localhost';
$user = 'root';
$pass = 'password';
$db = 'txproperties';

$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create contact_messages table
$sql = "CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    user_id INT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Contact messages table created successfully!<br>";
} else {
    echo "❌ Error creating table: " . $conn->error . "<br>";
}

// Add sample data
$sample_messages = [
    ['<PERSON>', '<EMAIL>', 'General Inquiry', 'Hello, I would like to know more about your properties in Dar es Salaam.', 1],
    ['<PERSON>', '<EMAIL>', 'Property Viewing Request', 'I am interested in viewing the luxury villa in Masaki.', NULL],
    ['Mike Johnson', '<EMAIL>', 'Pricing Information', 'What are the current market rates for apartments in Arusha?', 4]
];

$insert_sql = "INSERT INTO contact_messages (name, email, subject, message, user_id) VALUES (?, ?, ?, ?, ?)";
$stmt = $conn->prepare($insert_sql);

foreach ($sample_messages as $message) {
    $stmt->bind_param("ssssi", $message[0], $message[1], $message[2], $message[3], $message[4]);
    if ($stmt->execute()) {
        echo "✅ Sample message added: " . $message[2] . "<br>";
    } else {
        echo "❌ Error adding sample message: " . $stmt->error . "<br>";
    }
}

echo "<br>🎉 Contact messages system is ready!<br>";
echo "<a href='admin/contact_messages.php'>View Contact Messages in Admin Panel</a>";

$conn->close();
?> 