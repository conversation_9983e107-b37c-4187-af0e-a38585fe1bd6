/**
 * TX PROPERTIES - Main JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add active class to current nav item
    highlightCurrentPage();
    
    // Initialize animations for property cards
    initPropertyCardAnimations();
    
    // Make property cards clickable
    makePropertyCardsClickable();
    
    // Initialize property image slider if exists
    if (document.querySelector('.property-slider')) {
        initPropertySlider();
    }
    
    // Initialize form validations
    initFormValidations();
});

/**
 * Highlight current page in navigation
 */
function highlightCurrentPage() {
    const currentPage = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPage.endsWith(href)) {
            link.classList.add('active');
        }
    });
}

/**
 * Initialize animations for property cards
 */
function initPropertyCardAnimations() {
    const propertyCards = document.querySelectorAll('.property-card');
    
    // Add fade-in animation classes to property cards
    propertyCards.forEach((card, index) => {
        card.classList.add('fade-in');
        
        // Add delay based on card position
        const delayIndex = index % 3;
        if (delayIndex === 1) {
            card.classList.add('fade-in-delay-1');
        } else if (delayIndex === 2) {
            card.classList.add('fade-in-delay-2');
        }
    });
}

/**
 * Initialize property image slider
 */
function initPropertySlider() {
    // This is a placeholder for implementing a slider
    // You would typically use a library like Swiper or Slick
    console.log('Property slider initialized');
}

/**
 * Initialize form validations
 */
function initFormValidations() {
    const forms = document.querySelectorAll('.needs-validation');
    
    // Add Bootstrap form validation
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * Format currency 
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency code (default: TZS)
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'TZS') {
    return new Intl.NumberFormat('en-TZ', { 
        style: 'currency', 
        currency: currency,
        maximumFractionDigits: 0
    }).format(amount);
}

/**
 * Toggle favorite property
 * @param {Event} event - The click event
 * @param {number} propertyId - The property ID
 */
function toggleFavorite(event, propertyId) {
    event.preventDefault();
    
    // This would typically make an AJAX call to the server
    // For now, just toggle the icon
    const icon = event.currentTarget.querySelector('i');
    
    if (icon.classList.contains('far')) {
        // Add to favorites
        icon.classList.remove('far');
        icon.classList.add('fas');
        icon.classList.add('text-danger');
    } else {
        // Remove from favorites
        icon.classList.remove('fas');
        icon.classList.remove('text-danger');
        icon.classList.add('far');
    }
    
    console.log(`Toggled favorite for property ${propertyId}`);
}

/**
 * Filter properties
 * @param {Event} event - The form submit event
 */
function filterProperties(event) {
    event.preventDefault();
    
    // Get form values
    const location = document.getElementById('location').value;
    const propertyType = document.getElementById('property-type').value;
    const listingType = document.getElementById('listing-type').value;
    
    // Create query string for the search URL
    const queryParams = new URLSearchParams();
    
    if (location) queryParams.append('location', location);
    if (propertyType) queryParams.append('type', propertyType);
    if (listingType) queryParams.append('listing', listingType);
    
    // Redirect to search page with query parameters
    window.location.href = 'search.php?' + queryParams.toString();
}

/**
 * Make property cards clickable
 * When a user clicks anywhere on the card (except specific elements),
 * they will be redirected to the property detail page
 */
function makePropertyCardsClickable() {
    const propertyCards = document.querySelectorAll('.property-card');
    
    propertyCards.forEach(card => {
        // Find the property link to get the URL
        const propertyLink = card.querySelector('.property-title a');
        
        if (propertyLink) {
            const propertyUrl = propertyLink.getAttribute('href');
            
            // Make the entire card clickable
            card.addEventListener('click', function(e) {
                // Avoid triggering when clicking on buttons, links, or form elements
                const clickedElement = e.target;
                const isButton = clickedElement.tagName === 'BUTTON' || 
                                clickedElement.closest('button') || 
                                clickedElement.tagName === 'A' || 
                                clickedElement.closest('a');
                
                if (!isButton) {
                    window.location.href = propertyUrl;
                }
            });
            
            // Add cursor pointer style to indicate it's clickable
            card.style.cursor = 'pointer';
        }
    });
} 