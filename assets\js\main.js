/**
 * TX PROPERTIES - Main JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add active class to current nav item
    highlightCurrentPage();

    // Initialize animations for property cards
    initPropertyCardAnimations();

    // Make property cards clickable
    makePropertyCardsClickable();

    // Initialize property image slider if exists
    if (document.querySelector('.property-slider')) {
        initPropertySlider();
    }

    // Initialize form validations
    initFormValidations();

    // Initialize scroll animations
    initScrollAnimations();

    // Initialize navbar scroll behavior
    initNavbarScrollBehavior();

    // Initialize smooth scrolling
    initSmoothScrolling();

    // Initialize dark mode toggle
    initDarkModeToggle();
});

/**
 * Highlight current page in navigation
 */
function highlightCurrentPage() {
    const currentPage = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPage.endsWith(href)) {
            link.classList.add('active');
        }
    });
}

/**
 * Initialize animations for property cards
 */
function initPropertyCardAnimations() {
    const propertyCards = document.querySelectorAll('.property-card');
    
    // Add fade-in animation classes to property cards
    propertyCards.forEach((card, index) => {
        card.classList.add('fade-in');
        
        // Add delay based on card position
        const delayIndex = index % 3;
        if (delayIndex === 1) {
            card.classList.add('fade-in-delay-1');
        } else if (delayIndex === 2) {
            card.classList.add('fade-in-delay-2');
        }
    });
}

/**
 * Initialize property image slider
 */
function initPropertySlider() {
    // This is a placeholder for implementing a slider
    // You would typically use a library like Swiper or Slick
    console.log('Property slider initialized');
}

/**
 * Initialize form validations
 */
function initFormValidations() {
    const forms = document.querySelectorAll('.needs-validation');
    
    // Add Bootstrap form validation
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * Format currency 
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency code (default: TZS)
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'TZS') {
    return new Intl.NumberFormat('en-TZ', { 
        style: 'currency', 
        currency: currency,
        maximumFractionDigits: 0
    }).format(amount);
}

/**
 * Toggle favorite property
 * @param {Event} event - The click event
 * @param {number} propertyId - The property ID
 */
function toggleFavorite(event, propertyId) {
    event.preventDefault();
    
    // This would typically make an AJAX call to the server
    // For now, just toggle the icon
    const icon = event.currentTarget.querySelector('i');
    
    if (icon.classList.contains('far')) {
        // Add to favorites
        icon.classList.remove('far');
        icon.classList.add('fas');
        icon.classList.add('text-danger');
    } else {
        // Remove from favorites
        icon.classList.remove('fas');
        icon.classList.remove('text-danger');
        icon.classList.add('far');
    }
    
    console.log(`Toggled favorite for property ${propertyId}`);
}

/**
 * Filter properties
 * @param {Event} event - The form submit event
 */
function filterProperties(event) {
    event.preventDefault();
    
    // Get form values
    const location = document.getElementById('location').value;
    const propertyType = document.getElementById('property-type').value;
    const listingType = document.getElementById('listing-type').value;
    
    // Create query string for the search URL
    const queryParams = new URLSearchParams();
    
    if (location) queryParams.append('location', location);
    if (propertyType) queryParams.append('type', propertyType);
    if (listingType) queryParams.append('listing', listingType);
    
    // Redirect to search page with query parameters
    window.location.href = 'search.php?' + queryParams.toString();
}

/**
 * Make property cards clickable
 * When a user clicks anywhere on the card (except specific elements),
 * they will be redirected to the property detail page
 */
function makePropertyCardsClickable() {
    const propertyCards = document.querySelectorAll('.property-card');
    
    propertyCards.forEach(card => {
        // Find the property link to get the URL
        const propertyLink = card.querySelector('.property-title a');
        
        if (propertyLink) {
            const propertyUrl = propertyLink.getAttribute('href');
            
            // Make the entire card clickable
            card.addEventListener('click', function(e) {
                // Avoid triggering when clicking on buttons, links, or form elements
                const clickedElement = e.target;
                const isButton = clickedElement.tagName === 'BUTTON' || 
                                clickedElement.closest('button') || 
                                clickedElement.tagName === 'A' || 
                                clickedElement.closest('a');
                
                if (!isButton) {
                    window.location.href = propertyUrl;
                }
            });
            
            // Add cursor pointer style to indicate it's clickable
            card.style.cursor = 'pointer';
        }
    });
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const animateElements = document.querySelectorAll('.animate-on-scroll');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(element => {
        observer.observe(element);
    });

    // Add animate-on-scroll class to property cards
    const propertyCards = document.querySelectorAll('.property-card');
    propertyCards.forEach((card, index) => {
        card.classList.add('animate-on-scroll');
        card.style.animationDelay = `${index * 0.1}s`;
    });
}

/**
 * Initialize navbar scroll behavior
 */
function initNavbarScrollBehavior() {
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Add scrolled class when scrolling down
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        lastScrollTop = scrollTop;
    });
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            if (href === '#') return;

            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();

                const offsetTop = target.offsetTop - 100; // Account for fixed navbar

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Add loading animation to buttons on form submit
 */
function addButtonLoadingAnimation() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;

                // Re-enable after 3 seconds (fallback)
                setTimeout(() => {
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
    });
}

/**
 * Initialize dark mode toggle
 */
function initDarkModeToggle() {
    // Create theme toggle button
    const themeToggle = document.createElement('button');
    themeToggle.className = 'theme-toggle';
    themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
    themeToggle.setAttribute('aria-label', 'Toggle dark mode');
    themeToggle.setAttribute('title', 'Toggle dark/light mode');

    // Add to body
    document.body.appendChild(themeToggle);

    // Check for saved theme preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);

    // Update button icon based on current theme
    updateThemeIcon(themeToggle, currentTheme);

    // Add click event listener
    themeToggle.addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        // Update theme
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Update button icon
        updateThemeIcon(themeToggle, newTheme);

        // Add a subtle animation
        themeToggle.style.transform = 'translateY(-50%) rotate(360deg)';
        setTimeout(() => {
            themeToggle.style.transform = 'translateY(-50%) rotate(0deg)';
        }, 300);
    });
}

/**
 * Update theme toggle icon
 */
function updateThemeIcon(button, theme) {
    const icon = button.querySelector('i');
    if (theme === 'dark') {
        icon.className = 'fas fa-sun';
        button.setAttribute('title', 'Switch to light mode');
    } else {
        icon.className = 'fas fa-moon';
        button.setAttribute('title', 'Switch to dark mode');
    }
}

// Initialize button loading animations
document.addEventListener('DOMContentLoaded', addButtonLoadingAnimation);