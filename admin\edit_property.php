<?php
ob_start();
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}
require_once '../includes/config.php';
include 'admin_header.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: properties.php');
    exit;
}
$id = (int)$_GET['id'];

// Fetch property
$sql = "SELECT * FROM properties WHERE id = $id";
$result = $conn->query($sql);
$property = $result->fetch_assoc();
if (!$property) {
    echo '<div class="alert alert-danger">Property not found.</div>';
    include 'admin_footer.php';
    ob_end_flush();
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $type = trim($_POST['property_type']);
    $price = floatval($_POST['price']);
    $status = trim($_POST['status']);
    $location = trim($_POST['location']);
    $local_area = trim($_POST['local_area'] ?? '');

    // Check if local_area column exists
    $checkColumns = $conn->query("SHOW COLUMNS FROM properties LIKE 'local_area'");
    $hasLocalArea = $checkColumns->num_rows > 0;

    if ($hasLocalArea) {
        // Update with local_area column
        $sql = "UPDATE properties SET title=?, property_type=?, price=?, status=?, location=?, local_area=? WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ssdsssi', $title, $type, $price, $status, $location, $local_area, $id);
    } else {
        // Update without local_area column
        $sql = "UPDATE properties SET title=?, property_type=?, price=?, status=?, location=? WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ssdssi', $title, $type, $price, $status, $location, $id);
    }

    if ($stmt->execute()) {
        header('Location: properties.php');
        ob_end_flush();
        exit;
    } else {
        echo '<div class="alert alert-danger">Failed to update property.</div>';
    }
}
?>
<div class="container-fluid">
    <h1 class="mb-4">Edit Property</h1>
    <form method="post" class="mb-4" style="max-width:600px;">
        <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($property['title']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="property_type" class="form-label">Type</label>
            <input type="text" class="form-control" id="property_type" name="property_type" value="<?php echo htmlspecialchars($property['property_type']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="price" class="form-label">Price</label>
            <input type="number" class="form-control" id="price" name="price" value="<?php echo htmlspecialchars($property['price']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status" required>
                <option value="Available" <?php if ($property['status'] == 'Available') echo 'selected'; ?>>Available</option>
                <option value="Sold" <?php if ($property['status'] == 'Sold') echo 'selected'; ?>>Sold</option>
                <option value="Rented" <?php if ($property['status'] == 'Rented') echo 'selected'; ?>>Rented</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="location" class="form-label">Location</label>
            <input type="text" class="form-control" id="location" name="location" value="<?php echo htmlspecialchars($property['location']); ?>" required>
        </div>
        <?php
        // Check if local_area column exists before showing the field
        $checkColumns = $conn->query("SHOW COLUMNS FROM properties LIKE 'local_area'");
        $hasLocalArea = $checkColumns->num_rows > 0;
        if ($hasLocalArea):
        ?>
        <div class="mb-3">
            <label for="local_area" class="form-label">Local Area</label>
            <input type="text" class="form-control" id="local_area" name="local_area" value="<?php echo htmlspecialchars($property['local_area'] ?? ''); ?>">
        </div>
        <?php endif; ?>
        <button type="submit" class="btn btn-primary">Save Changes</button>
        <a href="properties.php" class="btn btn-secondary">Cancel</a>
    </form>
</div>
<?php include 'admin_footer.php'; ob_end_flush(); ?> 