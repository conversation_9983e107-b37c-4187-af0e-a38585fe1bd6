<?php 
require_once 'config.php';
require_once 'functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TX PROPERTIES - Real Estate Platform in Tanzania</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
       integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
       crossorigin=""/>
    
    <!-- Custom CSS -->
    <link href="<?php echo BASE_URL; ?>assets/css/style.css" rel="stylesheet">
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
       integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
       crossorigin=""></script>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top shadow-sm">
        <div class="container">
            <!-- Brand Logo -->
            <a class="navbar-brand fw-bold text-primary" href="<?php echo BASE_URL; ?>index.php">TX PROPERTIES</a>
            
            <!-- Mobile Toggle Button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>search_location.php">Search by Location</a>
                    </li>
                    
                    <?php if (isLoggedIn()): ?>
                        <?php if (isSeller()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                My Property
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>seller_dashboard.php">Seller Dashboard</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>add_property.php">Add Property</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>inbox.php">Inbox</a></li>
                            </ul>
                        </li>
                        <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>buyer_inbox.php">My Inbox
                                <?php if (isset($unreadCount) && $unreadCount > 0): ?>
                                <span class="badge bg-danger"><?php echo $unreadCount; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>about.php">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>contact.php">Contact Us</a>
                    </li>
                </ul>
                
                <div class="navbar-nav ms-auto">
                    <?php if (!isLoggedIn()): ?>
                        <a class="nav-link" href="<?php echo BASE_URL; ?>login.php">Login</a>
                        <a class="nav-link" href="<?php echo BASE_URL; ?>register.php">Register</a>
                    <?php else: ?>
                        <?php
                        // Check for unread messages
                        if (isLoggedIn()) {
                            $userId = $_SESSION['user_id'];
                            $unreadCount = countUnreadMessages($userId);
                        }
                        ?>
                        <?php if (isset($unreadCount) && $unreadCount > 0): ?>
                        <li class="nav-item me-2">
                            <a class="nav-link position-relative" href="<?php echo BASE_URL; ?><?php echo isSeller() ? 'inbox.php' : 'buyer_inbox.php'; ?>">
                                <i class="fas fa-envelope"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?php echo $unreadCount; ?>
                                    <span class="visually-hidden">unread messages</span>
                                </span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i><?php echo $_SESSION['username']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>profile.php">My Profile</a></li>
                                <?php if (isSeller()): ?>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>my_properties.php">My Properties</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>inbox.php">Inbox <?php if (isset($unreadCount) && $unreadCount > 0): ?><span class="badge bg-danger"><?php echo $unreadCount; ?></span><?php endif; ?></a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>buyer_inbox.php">Inbox <?php if (isset($unreadCount) && $unreadCount > 0): ?><span class="badge bg-danger"><?php echo $unreadCount; ?></span><?php endif; ?></a></li>
                                <?php endif; ?>
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/index.php">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content Container -->
    <div class="container my-4"> 