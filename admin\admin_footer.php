            </main>
        </div>
    </div>

    <!-- Admin Footer with Main Site Theme -->
    <footer class="admin-footer">
        <div class="container-fluid">
            <div class="row align-items-center py-4">
                <div class="col-md-6">
                    <div class="admin-footer-brand">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>TX Properties Admin Panel</strong>
                    </div>
                    <p class="admin-footer-text mb-0">
                        Manage your property listings and users with ease
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="admin-footer-links">
                        <a href="<?php echo $admin_base_url; ?>index.php" target="_blank" class="admin-footer-link">
                            <i class="fas fa-external-link-alt me-1"></i>View Website
                        </a>
                        <a href="settings.php" class="admin-footer-link">
                            <i class="fas fa-cog me-1"></i>Settings
                        </a>
                        <a href="logout.php" class="admin-footer-link text-danger">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </div>
                    <p class="admin-footer-copyright mb-0">
                        &copy; <?php echo date('Y'); ?> TX Properties. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="<?php echo $admin_base_url; ?>assets/js/admin.js"></script>

    <!-- Admin Theme JavaScript -->
    <script>
        // Add smooth scrolling and animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.card, .admin-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Add hover effects to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-1px)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add loading states to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                        submitBtn.disabled = true;
                    }
                });
            });
        });
    </script>
</body>
</html>