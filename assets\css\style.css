/* TX PROPERTIES - Modern Real Estate Platform - Updated */
/* CSS LOADED SUCCESSFULLY - If you see this comment in browser dev tools, CSS is loading */

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Professional Color Palette - Warm Earth Tones with Deep Blue/Gold Accents */
:root {
    /* Primary Colors - Warm Earth Tones */
    --primary-color: #2c5530;        /* Deep Forest Green */
    --secondary-color: #c17767;      /* Warm Terracotta */
    --accent-color: #1e3a8a;         /* Deep Blue */
    --gold-accent: #d4af37;          /* Gold */

    /* Neutral Earth Tones */
    --beige-light: #f7f3e9;          /* Light Beige */
    --beige-medium: #e8dcc0;         /* Medium Beige */
    --sage-green: #9caf88;           /* Soft Sage Green */
    --warm-gray: #6b7280;            /* Warm Gray */

    /* Functional Colors */
    --light-color: #fefefe;
    --dark-color: #1f2937;
    --text-color: #374151;
    --text-light: #6b7280;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* Status Colors */
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --info-color: #0284c7;

    /* Background Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--sage-green) 100%);
    --gradient-warm: linear-gradient(135deg, var(--beige-light) 0%, var(--beige-medium) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

/* Global Typography and Base Styles */
* {
    box-sizing: border-box;
}

/* Ensure our styles override Bootstrap */
html {
    scroll-behavior: smooth !important;
}

/* Force our background color */
html, body {
    background-color: var(--beige-light) !important;
    min-height: 100vh;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-color) !important;
    background-color: var(--beige-light) !important;
    scroll-behavior: smooth;
    position: relative;
}

/* CSS Loaded Indicator */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    z-index: 9999;
    animation: cssLoaded 2s ease-in-out;
}

@keyframes cssLoaded {
    0% { opacity: 1; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.3;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; font-weight: 600; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 500; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1.125rem; font-weight: 500; }

p {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.lead {
    font-size: 1.125rem;
    font-weight: 400;
    color: var(--text-light);
}

/* Links */
a {
    color: var(--accent-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* Modern Navigation Bar - Override Bootstrap */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border: none !important;
    border-bottom: none !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 1rem 0 !important;
    transition: all 0.3s ease;
    position: sticky !important;
    top: 0;
    z-index: 1030;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.12);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-family: 'Playfair Display', serif !important;
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    text-decoration: none !important;
    transition: all 0.3s ease;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.navbar-brand:hover {
    color: var(--accent-color) !important;
    transform: scale(1.05);
}

.navbar-nav {
    align-items: center;
}

.nav-link {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 0.95rem;
    color: var(--text-color) !important;
    padding: 0.75rem 1rem !important;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--beige-medium);
    transform: translateY(-2px);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--beige-medium);
    font-weight: 600;
}

/* Navbar Toggle Button */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2844, 85, 48, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Navbar Authentication Buttons */
.navbar-nav.ms-auto .nav-link {
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

/* Login Button Styling */
.navbar-nav.ms-auto .nav-link[href*="login.php"] {
    background-color: transparent;
    border: 2px solid var(--accent-color);
    color: var(--accent-color) !important;
    padding: 0.5rem 1.25rem !important;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.navbar-nav.ms-auto .nav-link[href*="login.php"]:hover {
    background-color: var(--accent-color);
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

/* Register Button Styling */
.navbar-nav.ms-auto .nav-link[href*="register.php"] {
    background: var(--gradient-primary);
    border: 2px solid transparent;
    color: white !important;
    padding: 0.5rem 1.25rem !important;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.2);
}

.navbar-nav.ms-auto .nav-link[href*="register.php"]:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--gold-accent) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(193, 119, 103, 0.4);
}

/* Active states for auth buttons */
.navbar-nav.ms-auto .nav-link[href*="login.php"]:active,
.navbar-nav.ms-auto .nav-link[href*="register.php"]:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Modern Hero Section */
.hero-section {
    background: linear-gradient(135deg,
        rgba(44, 85, 48, 0.9) 0%,
        rgba(30, 58, 138, 0.8) 50%,
        rgba(156, 175, 136, 0.9) 100%),
        url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    padding: 8rem 0 6rem;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.hero-section p {
    font-size: 1.25rem;
    max-width: 600px;
    margin-bottom: 2rem;
    opacity: 0.95;
    line-height: 1.6;
}

/* Modern Search Form */
.search-form {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    margin-top: -5rem;
    position: relative;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-form .form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.search-form .form-select,
.search-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--light-color);
    color: var(--text-color);
}

.search-form .form-select:focus,
.search-form .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.15);
    background-color: white;
    transform: translateY(-2px);
}

.search-form .form-select:hover,
.search-form .form-control:hover {
    border-color: var(--primary-color);
    background-color: white;
}

.btn-search {
    background: var(--gradient-primary);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

.btn-search:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--gold-accent) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(193, 119, 103, 0.4);
}

.btn-search:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

/* Modern Property Cards */
.property-card {
    border: none;
    border-radius: 16px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 2rem;
    height: 100%;
    position: relative;
    cursor: pointer;
}

.property-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(44, 85, 48, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.property-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.property-card:hover::before {
    opacity: 1;
}

.property-img {
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.property-card:hover .property-img {
    transform: scale(1.05);
}

.property-card .card-body {
    padding: 1.5rem;
    position: relative;
    z-index: 2;
}

.property-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
    line-height: 1.3;
}

.property-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.property-title a:hover {
    color: var(--accent-color);
}

.property-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.property-location {
    color: var(--text-light);
    font-size: 0.95rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.property-location i {
    margin-right: 0.5rem;
    color: var(--accent-color);
}

.property-features {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-light);
}

.property-feature {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.property-feature i {
    margin-right: 0.4rem;
    color: var(--primary-color);
}

/* Modern Section Titles */
.section-title {
    position: relative;
    margin-bottom: 3rem;
    padding-bottom: 1rem;
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--primary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-title::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: var(--gold-accent);
    border-radius: 2px;
}

/* Section Styling */
section {
    padding: 4rem 0;
    position: relative;
}

.why-choose-us {
    background: var(--gradient-warm);
    position: relative;
    overflow: hidden;
}

.why-choose-us::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(44, 85, 48, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.why-choose-us .container {
    position: relative;
    z-index: 2;
}

.why-choose-us .text-center {
    padding: 2rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    height: 100%;
}

.why-choose-us .text-center:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.why-choose-us .text-center i {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.why-choose-us .text-center:hover i {
    color: var(--accent-color);
    transform: scale(1.1);
}

/* Featured Locations */
.featured-locations .card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.featured-locations .card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.featured-locations .card-img-top {
    transition: transform 0.3s ease;
}

.featured-locations .card:hover .card-img-top {
    transform: scale(1.05);
}

.featured-locations .card-body {
    padding: 1.5rem;
}

.featured-locations .card-title {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.featured-locations .btn-outline-secondary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.featured-locations .btn-outline-secondary:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(193, 119, 103, 0.3);
}

/* Advanced Animations and Transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.8s ease forwards;
    opacity: 0;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease forwards;
    opacity: 0;
}

.slide-in-right {
    animation: slideInRight 0.8s ease forwards;
    opacity: 0;
}

.scale-in {
    animation: scaleIn 0.6s ease forwards;
    opacity: 0;
}

.fade-in-delay-1 {
    animation-delay: 0.2s;
}

.fade-in-delay-2 {
    animation-delay: 0.4s;
}

.fade-in-delay-3 {
    animation-delay: 0.6s;
}

.fade-in-delay-4 {
    animation-delay: 0.8s;
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Interactive Element Animations */
.card, .btn, .form-control, .form-select {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dropdown Animations */
.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.98);
    animation: scaleIn 0.3s ease forwards;
    transform-origin: top;
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
}

.dropdown-item:hover {
    background: var(--beige-medium);
    color: var(--primary-color);
    transform: translateX(5px);
}

/* Form Animations */
.form-floating label {
    transition: all 0.3s ease;
}

.form-control:focus ~ label,
.form-control:not(:placeholder-shown) ~ label {
    color: var(--accent-color);
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Dark Mode Styles */
[data-theme="dark"] {
    --primary-color: #4ade80;        /* Light Green */
    --secondary-color: #f97316;      /* Orange */
    --accent-color: #3b82f6;         /* Blue */
    --gold-accent: #fbbf24;          /* Gold */

    --beige-light: #1f2937;          /* Dark Gray */
    --beige-medium: #374151;         /* Medium Gray */
    --sage-green: #6b7280;           /* Gray */
    --warm-gray: #9ca3af;            /* Light Gray */

    --light-color: #111827;          /* Very Dark */
    --dark-color: #f9fafb;           /* Very Light */
    --text-color: #e5e7eb;           /* Light Gray */
    --text-light: #9ca3af;           /* Medium Gray */
    --border-color: #374151;         /* Dark Border */
    --shadow-color: rgba(0, 0, 0, 0.3);

    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--sage-green) 100%);
    --gradient-warm: linear-gradient(135deg, var(--beige-light) 0%, var(--beige-medium) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

[data-theme="dark"] body {
    background-color: var(--beige-light);
    color: var(--text-color);
}

[data-theme="dark"] .navbar {
    background: rgba(31, 41, 55, 0.95) !important;
}

[data-theme="dark"] .property-card {
    background: var(--beige-medium);
    color: var(--text-color);
}

[data-theme="dark"] .search-form {
    background: rgba(55, 65, 81, 0.98);
    color: var(--text-color);
}

[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg,
        rgba(31, 41, 55, 0.9) 0%,
        rgba(59, 130, 246, 0.8) 50%,
        rgba(107, 114, 128, 0.9) 100%),
        url('../images/hero-bg.jpg');
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1040;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.theme-toggle:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.theme-toggle:active {
    transform: translateY(-50%) scale(0.95);
}

@media (max-width: 768px) {
    .theme-toggle {
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 1200px) {
    .hero-section h1 {
        font-size: 3rem;
    }

    .search-form {
        padding: 2rem;
    }
}

@media (max-width: 992px) {
    /* Navbar adjustments */
    .navbar-nav.ms-auto {
        margin-top: 1rem;
        text-align: center;
    }

    .navbar-nav.ms-auto .nav-link[href*="login.php"],
    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        margin: 0.25rem auto;
        display: inline-block;
        width: auto;
        max-width: 200px;
    }

    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        margin-left: auto;
        margin-top: 0.5rem;
    }

    /* Hero section */
    .hero-section {
        padding: 6rem 0 4rem;
        background-attachment: scroll;
    }

    .hero-section h1 {
        font-size: 2.75rem;
    }

    /* Search form */
    .search-form {
        margin-top: -3rem;
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 768px) {
    /* Typography */
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }

    /* Hero section */
    .hero-section {
        padding: 5rem 0 3rem;
    }

    .hero-section h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-section p {
        font-size: 1.1rem;
    }

    /* Search form */
    .search-form {
        margin-top: -2rem;
        padding: 1.5rem;
        border-radius: 15px;
    }

    .search-form .form-select,
    .search-form .form-control {
        padding: 0.75rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .btn-search {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    /* Property cards */
    .property-card {
        margin-bottom: 1.5rem;
    }

    .property-card:hover {
        transform: translateY(-4px) scale(1.01);
    }

    /* Navbar buttons */
    .navbar-nav.ms-auto .nav-link[href*="login.php"],
    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        padding: 0.5rem 1rem !important;
        margin: 0.25rem 0.5rem;
        font-size: 0.9rem;
    }

    /* Section spacing */
    .section-title {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    /* Typography */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }

    /* Hero section */
    .hero-section {
        padding: 4rem 0 2rem;
    }

    .hero-section h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .hero-section p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    /* Search form */
    .search-form {
        margin-top: -1rem;
        padding: 1.25rem;
        border-radius: 12px;
    }

    .search-form .row {
        --bs-gutter-x: 0.75rem;
    }

    /* Buttons */
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.9rem;
    }

    .btn-lg {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
    }

    /* Navbar */
    .navbar-brand {
        font-size: 1.5rem;
    }

    .navbar-nav.ms-auto .nav-link[href*="login.php"],
    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        padding: 0.4rem 0.8rem !important;
        font-size: 0.85rem;
        border-radius: 20px;
        margin: 0.25rem;
    }

    /* Section titles */
    .section-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    /* Property cards */
    .property-img {
        height: 180px;
    }

    .property-title {
        font-size: 1.1rem;
    }

    .property-price {
        font-size: 1.2rem;
    }

    /* Footer */
    footer .social-icons a {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
    }
}

@media (max-width: 400px) {
    /* Extra small screens */
    .hero-section h1 {
        font-size: 1.75rem;
    }

    .search-form {
        padding: 1rem;
    }

    .property-card .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

/* Modern Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--primary-color) 100%);
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

footer .container {
    position: relative;
    z-index: 2;
}

footer h5, footer h6 {
    color: white;
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

footer p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

footer a:hover {
    color: var(--gold-accent);
    transform: translateX(3px);
}

footer a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gold-accent);
    transition: width 0.3s ease;
}

footer a:hover::after {
    width: 100%;
}

/* Social Icons */
.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-icons a:hover {
    background: var(--gold-accent);
    color: var(--dark-color);
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

.social-icons a::after {
    display: none;
}

/* Footer Lists */
footer ul {
    list-style: none;
    padding: 0;
}

footer ul li {
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

footer ul li:hover {
    transform: translateX(5px);
}

/* Footer Bottom */
footer hr {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 2rem 0;
}

/* Modern Button System */
.btn {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    text-transform: none;
    letter-spacing: 0.3px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Primary Button */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 85, 48, 0.4);
    color: white;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

/* Secondary Button */
.btn-secondary {
    background: var(--gradient-warm);
    color: var(--primary-color);
    border: 2px solid var(--secondary-color);
    box-shadow: 0 4px 15px rgba(193, 119, 103, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--gold-accent) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(193, 119, 103, 0.4);
}

/* Outline Buttons */
.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(44, 85, 48, 0.3);
}

.btn-outline-secondary {
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(193, 119, 103, 0.3);
}

/* Button Sizes */
.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 14px;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 10px;
}

/* Add Leaflet map styling */
#property-map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
    z-index: 1;  /* Ensure proper z-index for overlays */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Fix for map container on mobile devices */
@media (max-width: 768px) {
    #property-map {
        height: 300px;
    }
}

/* Ensure popup styling is consistent with site */
.leaflet-popup-content {
    font-family: inherit;
}

.leaflet-popup-content strong {
    color: #0d6efd;
}

/* Ensure the map controls are visible */
.leaflet-top, .leaflet-bottom {
    z-index: 1000;
}

/* Feature badges styling */
.feature-list {
    padding: 0;
    margin: 0;
}

.feature-badge {
    display: inline-block;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 30px;
    padding: 8px 15px;
    margin-bottom: 10px;
    margin-right: 8px;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s ease;
}

.feature-badge:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Property Details Page Styles */
.property-header h1 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.property-main {
    margin-bottom: 2rem;
}

.property-info .row {
    border-bottom: 1px solid #f0f0f0;
    padding: 0.5rem 0;
}

.property-info .row:last-child {
    border-bottom: none;
}

/* Make the layout responsive */
@media (max-width: 991px) {
    .property-main {
        flex-direction: column;
    }
    
    .property-main .col-lg-7,
    .property-main .col-lg-5 {
        width: 100%;
    }
    
    /* Hide the contact form in the right column on mobile */
    .d-lg-none {
        display: block !important;
    }
}

/* Enhance the property features display */
.list-group-item i {
    width: 20px;
    text-align: center;
}

/* Improve the map container */
#property-map {
    border: 1px solid #e9ecef;
}

/* Add shadow to cards for better visual hierarchy */
.card.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
    border: none;
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
}

.card.shadow-sm:hover {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.1) !important;
}

/* Style the carousel for better appearance */
.carousel-inner {
    border-radius: 8px;
    overflow: hidden;
}

.carousel-indicators {
    margin-bottom: 0.5rem;
}

.carousel-indicators button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
}

.carousel-indicators button.active {
    background-color: white;
}

/* Property Card Badges */
.property-card .badge {
    font-size: 0.95rem;
    padding: 0.5em 1em;
    border-radius: 0.5rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    z-index: 2;
}
.property-card .badge.bg-success { background-color: #28a745 !important; color: #fff; }
.property-card .badge.bg-danger { background-color: #dc3545 !important; color: #fff; }
.property-card .badge.bg-secondary { background-color: #6c757d !important; color: #fff; }
.property-card .badge.bg-info { background-color: #17a2b8 !important; color: #fff; }
.property-card .badge.bg-warning { background-color: #ffc107 !important; color: #212529; }

/* Sale and Rent specific colors */
.property-card .badge-sale { background-color: #007bff !important; color: #fff; }
.property-card .badge-rent { background-color: #fd7e14 !important; color: #fff; }

.position-absolute.top-0.start-0 { left: 0; top: 0; }
.position-absolute.top-0.end-0 { right: 0; top: 0; }
.position-absolute.top-0.start-50 { left: 50%; top: 0; transform: translateX(-50%); }

/* Authentication Pages Styling */
.auth-page-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

/* Enhanced Card Styling for Auth Forms */
.auth-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.auth-card .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
    border: none;
    padding: 1.5rem 2rem;
    text-align: center;
}

.auth-card .card-header h4 {
    margin: 0;
    font-weight: 600;
    font-size: 1.5rem;
}

.auth-card .card-body {
    padding: 2.5rem;
}

/* Enhanced Form Input Styling */
.auth-form .form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.auth-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.auth-form .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background-color: white;
    transform: translateY(-2px);
}

.auth-form .form-control:hover {
    border-color: #ced4da;
    background-color: white;
}

/* Enhanced Submit Button */
.auth-form .btn-submit {
    background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.auth-form .btn-submit:hover {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(44, 62, 80, 0.3);
}

.auth-form .btn-submit:active {
    transform: translateY(0);
    box-shadow: 0 4px 10px rgba(44, 62, 80, 0.2);
}

/* Form Check Styling */
.auth-form .form-check-input {
    border-radius: 4px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.auth-form .form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.auth-form .form-check-label {
    font-size: 0.95rem;
    color: var(--gray-color);
}

/* Links Styling */
.auth-links a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Form Validation Styling */
.auth-form .form-control.is-invalid {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
    background-color: #fff5f5;
}

.auth-form .form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    background-color: #f8fff8;
}

.auth-form .invalid-feedback {
    display: block;
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.auth-form .valid-feedback {
    display: block;
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.auth-form .form-text {
    color: var(--gray-color);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

/* Alert Styling for Auth Pages */
.auth-card .alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.auth-card .alert-danger {
    background-color: #fff5f5;
    color: var(--secondary-color);
    border-left: 4px solid var(--secondary-color);
}

.auth-card .alert-success {
    background-color: #f8fff8;
    color: #28a745;
    border-left: 4px solid #28a745;
}

/* Mobile Responsive Adjustments */
@media (max-width: 991px) {
    /* Navbar Auth Buttons on Mobile */
    .navbar-nav.ms-auto {
        margin-top: 1rem;
        text-align: center;
    }

    .navbar-nav.ms-auto .nav-link[href*="login.php"],
    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        margin: 0.25rem auto;
        display: inline-block;
        width: auto;
        max-width: 200px;
    }

    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        margin-left: auto;
        margin-top: 0.5rem;
    }
}

@media (max-width: 768px) {
    /* Auth Container Mobile */
    .auth-container {
        padding: 1rem 0;
        min-height: auto;
    }

    .auth-card {
        margin: 1rem;
        border-radius: 10px;
    }

    .auth-card .card-header {
        padding: 1.25rem 1.5rem;
    }

    .auth-card .card-header h4 {
        font-size: 1.25rem;
    }

    .auth-card .card-body {
        padding: 1.5rem;
    }

    /* Form adjustments for mobile */
    .auth-form .form-control {
        padding: 0.75rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .auth-form .btn-submit {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    /* Navbar buttons mobile stacking */
    .navbar-nav.ms-auto .nav-link[href*="login.php"],
    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        padding: 0.5rem 1rem !important;
        margin: 0.25rem 0.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .auth-card {
        margin: 0.5rem;
    }

    .auth-card .card-body {
        padding: 1.25rem;
    }

    .auth-form .form-control {
        padding: 0.625rem;
    }

    /* Smaller navbar buttons on very small screens */
    .navbar-nav.ms-auto .nav-link[href*="login.php"],
    .navbar-nav.ms-auto .nav-link[href*="register.php"] {
        padding: 0.4rem 0.8rem !important;
        font-size: 0.85rem;
        border-radius: 20px;
    }
}

/* Enhanced Auth Form Styling */
.form-control:focus {
    border-color: #2c5530 !important;
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25) !important;
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: #9caf88 !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.btn:active {
    transform: translateY(0) !important;
}

/* Form validation styling */
.was-validated .form-control:valid {
    border-color: #28a745 !important;
    background-image: none;
}

.was-validated .form-control:invalid {
    border-color: #dc3545 !important;
    background-image: none;
}