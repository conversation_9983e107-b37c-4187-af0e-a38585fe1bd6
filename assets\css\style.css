/* TX PROPERTIES - Custom CSS Styles */

/* Global Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e74c3c;
    --accent-color: #3498db;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333333;
    --gray-color: #6c757d;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: #f9f9f9;
}

/* Navigation Bar */
.navbar {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 6rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 10px 10px;
}

.hero-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.hero-section p {
    font-size: 1.2rem;
    max-width: 600px;
    margin-bottom: 2rem;
}

/* Search Form */
.search-form {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-top: -4rem;
    position: relative;
    z-index: 10;
}

.btn-search {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-search:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* Property Cards */
.property-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 2rem;
    height: 100%;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.property-img {
    height: 200px;
    object-fit: cover;
}

.property-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.property-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.property-location {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.property-location i {
    margin-right: 0.25rem;
}

.property-features {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--gray-color);
}

.property-feature i {
    margin-right: 0.25rem;
}

/* Section Titles */
.section-title {
    position: relative;
    margin-bottom: 2.5rem;
    padding-bottom: 1rem;
    text-align: center;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.8s ease forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.2s;
}

.fade-in-delay-2 {
    animation-delay: 0.4s;
}

.fade-in-delay-3 {
    animation-delay: 0.6s;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 4rem 0;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .search-form {
        margin-top: 0;
    }
    
    .property-card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: white;
}

footer a:hover {
    color: var(--secondary-color) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1a252f;
    border-color: #1a252f;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* Add Leaflet map styling */
#property-map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
    z-index: 1;  /* Ensure proper z-index for overlays */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Fix for map container on mobile devices */
@media (max-width: 768px) {
    #property-map {
        height: 300px;
    }
}

/* Ensure popup styling is consistent with site */
.leaflet-popup-content {
    font-family: inherit;
}

.leaflet-popup-content strong {
    color: #0d6efd;
}

/* Ensure the map controls are visible */
.leaflet-top, .leaflet-bottom {
    z-index: 1000;
}

/* Feature badges styling */
.feature-list {
    padding: 0;
    margin: 0;
}

.feature-badge {
    display: inline-block;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 30px;
    padding: 8px 15px;
    margin-bottom: 10px;
    margin-right: 8px;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s ease;
}

.feature-badge:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Property Details Page Styles */
.property-header h1 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.property-main {
    margin-bottom: 2rem;
}

.property-info .row {
    border-bottom: 1px solid #f0f0f0;
    padding: 0.5rem 0;
}

.property-info .row:last-child {
    border-bottom: none;
}

/* Make the layout responsive */
@media (max-width: 991px) {
    .property-main {
        flex-direction: column;
    }
    
    .property-main .col-lg-7,
    .property-main .col-lg-5 {
        width: 100%;
    }
    
    /* Hide the contact form in the right column on mobile */
    .d-lg-none {
        display: block !important;
    }
}

/* Enhance the property features display */
.list-group-item i {
    width: 20px;
    text-align: center;
}

/* Improve the map container */
#property-map {
    border: 1px solid #e9ecef;
}

/* Add shadow to cards for better visual hierarchy */
.card.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
    border: none;
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
}

.card.shadow-sm:hover {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.1) !important;
}

/* Style the carousel for better appearance */
.carousel-inner {
    border-radius: 8px;
    overflow: hidden;
}

.carousel-indicators {
    margin-bottom: 0.5rem;
}

.carousel-indicators button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
}

.carousel-indicators button.active {
    background-color: white;
}

/* Property Card Badges */
.property-card .badge {
    font-size: 0.95rem;
    padding: 0.5em 1em;
    border-radius: 0.5rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    z-index: 2;
}
.property-card .badge.bg-success { background-color: #28a745 !important; color: #fff; }
.property-card .badge.bg-danger { background-color: #dc3545 !important; color: #fff; }
.property-card .badge.bg-secondary { background-color: #6c757d !important; color: #fff; }
.property-card .badge.bg-info { background-color: #17a2b8 !important; color: #fff; }
.property-card .badge.bg-warning { background-color: #ffc107 !important; color: #212529; }

/* Sale and Rent specific colors */
.property-card .badge-sale { background-color: #007bff !important; color: #fff; }
.property-card .badge-rent { background-color: #fd7e14 !important; color: #fff; }

.position-absolute.top-0.start-0 { left: 0; top: 0; }
.position-absolute.top-0.end-0 { right: 0; top: 0; }
.position-absolute.top-0.start-50 { left: 50%; top: 0; transform: translateX(-50%); } 