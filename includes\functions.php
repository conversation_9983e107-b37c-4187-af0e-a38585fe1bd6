<?php
/**
 * TX PROPERTIES - Functions for property operations
 */

/**
 * Get latest properties from database
 * 
 * @param int $limit Number of properties to retrieve
 * @param bool $featured Whether to get only featured properties
 * @return array Array of property data
 */
function getLatestProperties($limit = 6, $featured = false) {
    global $conn;
    
    $featuredCondition = $featured ? " AND featured = 1" : "";
    $sql = "SELECT p.*, u.username as seller_name, 
                  (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image 
           FROM properties p
           JOIN users u ON p.seller_id = u.id
           WHERE p.status = 'Available'{$featuredCondition}
           ORDER BY p.created_at DESC 
           LIMIT ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $properties = [];
    while ($row = $result->fetch_assoc()) {
        // If no primary image is set, use a default image
        if (empty($row['primary_image'])) {
            $row['primary_image'] = 'assets/images/property-default.jpg';
        }
        
        // Format the location display (local_area, location)
        if (!empty($row['local_area'])) {
            $row['location_display'] = $row['local_area'] . ', ' . $row['location'];
        } else {
            $row['location_display'] = $row['location'];
        }
        
        $properties[] = $row;
    }
    
    return $properties;
}

/**
 * Get all property locations for dropdowns
 */
function getPropertyLocations() {
    // Return fixed list of Tanzania regions
    return [
        'Arusha',
        'Dar es Salaam',
        'Dodoma',
        'Mbeya',
        'Tanga',
        'Zanzibar'
    ];
}

/**
 * Search properties by criteria
 * 
 * @param array $criteria Search criteria
 * @return array Array of matching properties
 */
function searchProperties($criteria) {
    global $conn;
    
    $conditions = [];
    $params = [];
    $types = "";
    
    // Build search conditions
    if (!empty($criteria['location'])) {
        $conditions[] = "p.location = ?";
        $params[] = $criteria['location'];
        $types .= "s";
    }
    
    if (!empty($criteria['type'])) {
        $conditions[] = "p.property_type = ?";
        $params[] = $criteria['type'];
        $types .= "s";
    }
    
    if (!empty($criteria['listing'])) {
        $conditions[] = "p.listing_type = ?";
        $params[] = $criteria['listing'];
        $types .= "s";
    }
    
    // Always get available properties
    $conditions[] = "p.status = 'Available'";
    
    // Combine all conditions
    $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
    
    $sql = "SELECT p.*, u.username as seller_name, 
                  (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image 
           FROM properties p
           JOIN users u ON p.seller_id = u.id
           {$whereClause}
           ORDER BY p.created_at DESC";
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $properties = [];
    while ($row = $result->fetch_assoc()) {
        // If no primary image is set, use a default image
        if (empty($row['primary_image'])) {
            $row['primary_image'] = 'assets/images/property-default.jpg';
        }
        
        // Format the location display (local_area, location)
        if (!empty($row['local_area'])) {
            $row['location_display'] = $row['local_area'] . ', ' . $row['location'];
        } else {
            $row['location_display'] = $row['location'];
        }
        
        $properties[] = $row;
    }
    
    return $properties;
}

/**
 * Get property details by ID
 * 
 * @param int $id Property ID
 * @return array|null Property details or null if not found
 */
function getPropertyById($id) {
    global $conn;
    
    $sql = "SELECT p.*, u.username as seller_name, u.email as seller_email, u.phone as seller_phone
            FROM properties p
            JOIN users u ON p.seller_id = u.id
            WHERE p.id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    // Increment view count
    $updateSql = "UPDATE properties SET views = views + 1 WHERE id = ?";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bind_param("i", $id);
    $updateStmt->execute();
    
    return $result->fetch_assoc();
}

/**
 * Get property images by property ID
 * 
 * @param int $propertyId Property ID
 * @return array Array of image paths
 */
function getPropertyImages($propertyId) {
    global $conn;
    
    $sql = "SELECT * FROM property_images WHERE property_id = ? ORDER BY is_primary DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $propertyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $images = [];
    while ($row = $result->fetch_assoc()) {
        $images[] = $row;
    }
    
    // If no images found, return a default image
    if (empty($images)) {
        $images[] = [
            'id' => 0,
            'property_id' => $propertyId,
            'image_path' => 'assets/images/property_placeholder.jpg',
            'is_primary' => 1
        ];
    }
    
    return $images;
}

/**
 * Format price based on listing type
 * 
 * @param float $price The price amount
 * @param string $listingType 'Sale' or 'Rent'
 * @return string Formatted price with appropriate suffix
 */
function formatPrice($price, $listingType) {
    $formattedPrice = number_format($price, 0, '.', ',');
    
    if ($listingType === 'Rent') {
        return "TZS " . $formattedPrice . "/month";
    } else {
        return "TZS " . $formattedPrice;
    }
}

/**
 * Function to protect seller-only pages
 * 
 * @param string $redirect_to The page to redirect to after login
 * @return void
 */
function require_seller_login($redirect_to = '') {
    // Check if user is logged in
    if (!isLoggedIn()) {
        $redirectUrl = 'login.php';
        if (!empty($redirect_to)) {
            $redirectUrl .= '?redirect=' . urlencode($redirect_to);
        }
        redirect($redirectUrl);
    }
    
    // Check if user is a seller
    if (!isSeller()) {
        // If logged in but not a seller, redirect to home with message
        $_SESSION['error_message'] = "You must be a seller to access this page.";
        redirect('index.php');
    }
}

// Functions for getting property details and images are already defined above

/**
 * Function to process payment
 *
 * @param int $propertyId The property ID
 * @param int $buyerId The buyer ID
 * @param float $amount The payment amount
 * @param string $paymentMethod The payment method
 * @return bool|string True if successful, error message if failed
 */
function processPayment($propertyId, $buyerId, $amount, $paymentMethod = 'Flutterwave') {
    global $conn;
    
    // Get property details
    $sql = "SELECT seller_id FROM properties WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $propertyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        return "Property not found.";
    }
    
    $property = $result->fetch_assoc();
    $sellerId = $property['seller_id'];
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate transaction ID
        $transactionId = 'TXN' . time() . rand(1000, 9999);
        
        // Insert payment record
        $sql = "INSERT INTO payments (property_id, buyer_id, seller_id, amount, status, transaction_id, payment_method) 
                VALUES (?, ?, ?, ?, 'completed', ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiidss", $propertyId, $buyerId, $sellerId, $amount, $transactionId, $paymentMethod);
        $stmt->execute();
        
        // Update seller's balance
        $sql = "UPDATE users SET balance = balance + ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("di", $amount, $sellerId);
        $stmt->execute();
        
        // Update property status if it's a sale (not for rent)
        $sql = "UPDATE properties SET status = 'Reserved' WHERE id = ? AND listing_type = 'Sale'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $propertyId);
        $stmt->execute();
        
        // Commit transaction
        $conn->commit();
        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        return "Payment failed: " . $e->getMessage();
    }
}

/**
 * Count unread messages for a user
 * 
 * @param int $userId The user ID
 * @return int Number of unread messages
 */
function countUnreadMessages($userId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) as unread_count FROM messages WHERE receiver_id = ? AND read_status = 0";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc();
    
    return (int) $data['unread_count'];
}
?> 