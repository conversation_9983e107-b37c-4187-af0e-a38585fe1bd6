<?php
// Test Flutterwave Payment Flow - Complete Implementation Verification
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Flutterwave Payment Flow</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>💳 Flutterwave Payment Flow Test</h1>";

// Step 1: Implementation Summary
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-check me-2'></i>Implementation Summary</h3>";
echo "</div>";
echo "<div class='card-body'>";

$features = [
    'Property Status Update' => 'Properties marked as "Sold" after successful payment',
    'Listing Filtering' => 'Sold properties hidden from main listings and search',
    'Buyer Messaging' => 'Purchase confirmation sent to buyer inbox',
    'Seller Messaging' => 'Sale notification sent to seller inbox',
    'Payment Processing' => 'Flutterwave integration with transaction verification',
    'Success Page' => 'Detailed confirmation page after payment',
    'UI Feedback' => 'Clear success messages and status updates'
];

echo "<div class='row'>";
foreach ($features as $feature => $description) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card h-100'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title text-success'><i class='fas fa-check-circle me-2'></i>$feature</h6>";
    echo "<p class='card-text text-muted'>$description</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "</div></div>";

// Step 2: Test Property Status Filtering
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-filter me-2'></i>Property Listing Filtering Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Test the filtering functions
$allProperties = $conn->query("SELECT id, title, status FROM properties ORDER BY id")->fetch_all(MYSQLI_ASSOC);
$availableProperties = getLatestProperties(10);
$searchResults = searchProperties(['location' => '']); // Empty search to get all available

echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<h5>All Properties in Database:</h5>";
echo "<div class='list-group'>";
foreach ($allProperties as $prop) {
    $badgeClass = $prop['status'] === 'Available' ? 'bg-success' : 'bg-danger';
    echo "<div class='list-group-item d-flex justify-content-between align-items-center'>";
    echo "<span>#{$prop['id']} " . htmlspecialchars(substr($prop['title'], 0, 20)) . "...</span>";
    echo "<span class='badge $badgeClass'>{$prop['status']}</span>";
    echo "</div>";
}
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<h5>Latest Properties (Public):</h5>";
echo "<div class='list-group'>";
foreach ($availableProperties as $prop) {
    echo "<div class='list-group-item'>";
    echo "<span>#{$prop['id']} " . htmlspecialchars(substr($prop['title'], 0, 20)) . "...</span>";
    echo "<span class='badge bg-success ms-2'>{$prop['status']}</span>";
    echo "</div>";
}
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<h5>Search Results:</h5>";
echo "<div class='list-group'>";
foreach ($searchResults as $prop) {
    echo "<div class='list-group-item'>";
    echo "<span>#{$prop['id']} " . htmlspecialchars(substr($prop['title'], 0, 20)) . "...</span>";
    echo "<span class='badge bg-success ms-2'>{$prop['status']}</span>";
    echo "</div>";
}
echo "</div>";
echo "</div>";
echo "</div>";

$soldCount = count(array_filter($allProperties, function($p) { return $p['status'] === 'Sold'; }));
$availableCount = count($availableProperties);

echo "<div class='alert alert-info mt-3'>";
echo "<h6><i class='fas fa-info-circle me-2'></i>Filtering Results:</h6>";
echo "<ul class='mb-0'>";
echo "<li><strong>Total Properties:</strong> " . count($allProperties) . "</li>";
echo "<li><strong>Sold Properties:</strong> $soldCount (hidden from public)</li>";
echo "<li><strong>Available Properties:</strong> $availableCount (shown in listings)</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

// Step 3: Test Message System
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-envelope me-2'></i>Message System Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Check recent payment-related messages
$recentMessages = $conn->query("
    SELECT m.*, u.username as sender_name, p.title as property_title 
    FROM messages m 
    LEFT JOIN users u ON m.sender_id = u.id 
    LEFT JOIN properties p ON m.property_id = p.id 
    WHERE m.subject LIKE '%Payment%' OR m.subject LIKE '%Property%' OR m.subject LIKE '%Sold%'
    ORDER BY m.created_at DESC 
    LIMIT 5
")->fetch_all(MYSQLI_ASSOC);

if (empty($recentMessages)) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>No payment-related messages found. Complete a test payment to see messages.";
    echo "</div>";
} else {
    echo "<h5>Recent Payment Messages:</h5>";
    echo "<div class='list-group'>";
    foreach ($recentMessages as $msg) {
        $senderName = $msg['sender_name'] ?: 'System';
        echo "<div class='list-group-item'>";
        echo "<div class='d-flex w-100 justify-content-between'>";
        echo "<h6 class='mb-1'>" . htmlspecialchars($msg['subject']) . "</h6>";
        echo "<small>" . date('M j, Y', strtotime($msg['created_at'])) . "</small>";
        echo "</div>";
        echo "<p class='mb-1'>" . htmlspecialchars(substr($msg['message'], 0, 100)) . "...</p>";
        echo "<small>From: $senderName | Property: " . htmlspecialchars($msg['property_title'] ?: 'N/A') . "</small>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div></div>";

// Step 4: Payment Flow Test
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'><i class='fas fa-credit-card me-2'></i>Payment Flow Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>Test the Complete Payment Flow:</h5>";
echo "<div class='row'>";

// Get some test properties
$testProperties = $conn->query("SELECT id, title, status, price FROM properties WHERE status = 'Available' LIMIT 3")->fetch_all(MYSQLI_ASSOC);

foreach ($testProperties as $prop) {
    echo "<div class='col-md-4 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>Property #{$prop['id']}</h6>";
    echo "<p class='card-text'>" . htmlspecialchars(substr($prop['title'], 0, 30)) . "...</p>";
    echo "<p class='text-success'><strong>TZS " . number_format($prop['price']) . "</strong></p>";
    echo "<span class='badge bg-success mb-2'>{$prop['status']}</span>";
    echo "<div class='d-grid gap-2'>";
    echo "<a href='property.php?id={$prop['id']}' class='btn btn-primary btn-sm' target='_blank'>View Property</a>";
    echo "<a href='pay.php?property_id={$prop['id']}' class='btn btn-success btn-sm' target='_blank'>Test Payment</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h6><i class='fas fa-info-circle me-2'></i>Payment Flow Steps:</h6>";
echo "<ol class='mb-0'>";
echo "<li>Click 'Test Payment' on an available property</li>";
echo "<li>Complete Flutterwave payment process</li>";
echo "<li>Verify property status changes to 'Sold'</li>";
echo "<li>Check that property disappears from listings</li>";
echo "<li>Verify messages sent to buyer and seller</li>";
echo "<li>Confirm success page displays correctly</li>";
echo "</ol>";
echo "</div>";

echo "</div></div>";

// Step 5: Expected Behavior
echo "<div class='card'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-clipboard-check me-2'></i>Expected Behavior</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>✅ After Successful Payment:</h5>";
echo "<ul>";
echo "<li>Property status updated to 'Sold'</li>";
echo "<li>Property hidden from public listings</li>";
echo "<li>Property hidden from search results</li>";
echo "<li>Buyer receives purchase confirmation message</li>";
echo "<li>Seller receives sale notification message</li>";
echo "<li>Payment record created in database</li>";
echo "<li>Success page displayed with details</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>📧 Message Content:</h5>";
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h6 class='mb-0'>Buyer Message:</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<small>\"You have successfully purchased the property titled [Property Title]. Thank you for your payment.\"</small>";
echo "</div>";
echo "</div>";

echo "<div class='card mt-2'>";
echo "<div class='card-header'>";
echo "<h6 class='mb-0'>Seller Message:</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<small>\"Your property titled [Property Title] has been sold via Flutterwave.\"</small>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='index.php' class='btn btn-primary' target='_blank'><i class='fas fa-home me-2'></i>Test Homepage Listings</a>";
echo "<a href='search.php' class='btn btn-info' target='_blank'><i class='fas fa-search me-2'></i>Test Search Page</a>";
echo "<a href='login.php' class='btn btn-success'><i class='fas fa-sign-in-alt me-2'></i>Login as Buyer</a>";
echo "<a href='my_paid_properties.php' class='btn btn-warning' target='_blank'><i class='fas fa-list me-2'></i>My Purchases</a>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Final Status
echo "<div class='text-center mt-4'>";
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>✅ FLUTTERWAVE PAYMENT FLOW IMPLEMENTED!</h4>";
echo "<p class='mb-0'>Complete payment workflow with property status updates, messaging, and listing filtering.</p>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
