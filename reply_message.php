<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php?redirect=inbox.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Check if message ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('inbox.php');
}

$messageId = (int) $_GET['id'];

// Get original message details
$sql = "SELECT m.*, p.title as property_title, u.username as sender_name, u.id as sender_id 
        FROM messages m 
        LEFT JOIN properties p ON m.property_id = p.id 
        LEFT JOIN users u ON m.sender_id = u.id 
        WHERE m.id = ? AND m.receiver_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $messageId, $userId);
$stmt->execute();
$result = $stmt->get_result();

// Check if message exists and belongs to the user
if ($result->num_rows !== 1) {
    redirect('inbox.php');
}

$originalMessage = $result->fetch_assoc();

// Initialize variables
$subject = 'Re: ' . $originalMessage['subject'];
$message = '';
$error = '';
$success = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    
    // Validate inputs
    if (empty($subject)) {
        $error = "Subject is required.";
    } elseif (empty($message)) {
        $error = "Message is required.";
    } else {
        // Insert reply
        $receiverId = $originalMessage['sender_id'];
        $propertyId = $originalMessage['property_id'];
        
        $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, read_status) 
                VALUES (?, ?, ?, ?, ?, 0)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiiss", $userId, $receiverId, $propertyId, $subject, $message);
        
        if ($stmt->execute()) {
            $success = "Reply sent successfully!";
            // Clear form
            $message = '';
        } else {
            $error = "Error sending reply. Please try again.";
        }
    }
}
?>

<!-- Reply Message Page -->
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">Reply to Message</h1>
                <div>
                    <a href="inbox.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Inbox
                    </a>
                </div>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Original Message</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p class="mb-1"><strong>From:</strong> <?php echo $originalMessage['sender_name']; ?></p>
                        <p class="mb-1"><strong>Subject:</strong> <?php echo $originalMessage['subject']; ?></p>
                        <p class="mb-1"><strong>Date:</strong> <?php echo date('F d, Y H:i', strtotime($originalMessage['created_at'])); ?></p>
                        
                        <?php if ($originalMessage['property_id']): ?>
                            <p class="mb-1">
                                <strong>Property:</strong>
                                <a href="property.php?id=<?php echo $originalMessage['property_id']; ?>" class="text-decoration-none">
                                    <?php echo $originalMessage['property_title']; ?>
                                </a>
                            </p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="message-content p-3 bg-light rounded mb-3">
                        <?php echo nl2br($originalMessage['message']); ?>
                    </div>
                </div>
            </div>
            
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Your Reply</h5>
                </div>
                <div class="card-body">
                    <form action="reply_message.php?id=<?php echo $messageId; ?>" method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo $subject; ?>" required>
                            <div class="invalid-feedback">
                                Please provide a subject.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required><?php echo $message; ?></textarea>
                            <div class="invalid-feedback">
                                Please provide a message.
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i> Send Reply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?> 