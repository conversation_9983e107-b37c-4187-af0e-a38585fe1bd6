<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check authentication BEFORE including header
if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    redirect('login.php');
}

$buyerId = $_SESSION['user_id'];
$buyerName = $_SESSION['username'];
$buyerEmail = $_SESSION['email'];
$propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;

if (!$propertyId) {
    redirect('index.php');
}

// Get property details
$property = getPropertyById($propertyId);
if (!$property) {
    redirect('index.php');
}

// All validation done, now safe to include header
require_once 'includes/header.php';

$amount = $property['price'];
$sellerId = $property['seller_id'];
$sellerName = htmlspecialchars($property['seller_name']);

?>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg">
                <div class="card-body p-4">
                    <h3 class="mb-3 text-center">Pay for Property</h3>
                    <div class="mb-3">
                        <strong>Buyer:</strong> <?php echo htmlspecialchars($buyerName); ?><br>
                        <strong>Email:</strong> <?php echo htmlspecialchars($buyerEmail); ?><br>
                        <strong>Property ID:</strong> <?php echo $propertyId; ?>
                    </div>
                    <div class="d-grid mt-4">
                        <button type="button" class="btn btn-success btn-lg" id="payBtn">Pay TZS <?php echo number_format($amount, 0); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://checkout.flutterwave.com/v3.js"></script>
<script>
document.getElementById('payBtn').onclick = function () {
    FlutterwaveCheckout({
        public_key: "FLWPUBK_TEST-d4f1bb786cb335b4112efdcd3f38cfb6-X",
        tx_ref: "txref_" + Date.now(),
        amount: <?php echo $amount; ?>,
        currency: "TZS",
        payment_options: "card, mobilemoney",
        customer: {
            email: "<?php echo $buyerEmail; ?>",
            name: "<?php echo $buyerName; ?>"
        },
        callback: function (data) {
            if (data.status === 'successful' || data.status === 'completed') {
                window.location.href = "process_payment.php?transaction_id=" + data.id +
                    "&property_id=<?php echo $propertyId; ?>" +
                    "&amount=<?php echo $amount; ?>" +
                    "&seller_id=<?php echo $sellerId; ?>" +
                    "&seller_name=<?php echo urlencode($sellerName); ?>";
            } else {
                alert('Payment was not successful.');
            }
        },
        customizations: {
            title: "Property Payment",
            description: "Payment for Property ID <?php echo $propertyId; ?>",
            logo: "assets/images/logo.png"
        }
    });
};
</script>
<?php require_once 'includes/footer.php'; ?> 