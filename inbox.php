<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php?redirect=inbox.php');
}

// Redirect buyers to their inbox
if (!isSeller()) {
    redirect('buyer_inbox.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Get messages
$sql = "SELECT m.*, p.title as property_title, u.username as sender_name 
        FROM messages m 
        LEFT JOIN properties p ON m.property_id = p.id 
        LEFT JOIN users u ON m.sender_id = u.id 
        WHERE m.receiver_id = ? 
        ORDER BY m.created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$messages = [];
while ($row = $result->fetch_assoc()) {
    $messages[] = $row;
}

// Count unread messages
$unreadCount = 0;
foreach ($messages as $message) {
    if (!$message['read_status']) {
        $unreadCount++;
    }
}

// Mark message as read if requested
if (isset($_GET['read']) && is_numeric($_GET['read'])) {
    $messageId = (int) $_GET['read'];
    
    // Verify the message belongs to the user
    $sql = "UPDATE messages SET read_status = 1 WHERE id = ? AND receiver_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $messageId, $userId);
    $stmt->execute();
    
    // Redirect to remove the query parameter
    redirect('inbox.php');
}

// Delete message if requested
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $messageId = (int) $_GET['delete'];
    
    // Verify the message belongs to the user
    $sql = "DELETE FROM messages WHERE id = ? AND receiver_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $messageId, $userId);
    $stmt->execute();
    
    // Redirect to remove the query parameter
    redirect('inbox.php?deleted=1');
}

?>

<!-- Inbox Page -->
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">My Inbox</h1>
            
            <?php if (isset($_GET['deleted'])): ?>
                <div class="alert alert-success">Message deleted successfully.</div>
            <?php endif; ?>
            
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0 d-inline-block">Messages</h5>
                        <?php if ($unreadCount > 0): ?>
                            <span class="badge bg-danger ms-2"><?php echo $unreadCount; ?> unread</span>
                        <?php endif; ?>
                    </div>
                    <div>
                        <a href="compose.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-pen me-1"></i> Compose
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($messages)): ?>
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-inbox fa-4x text-muted"></i>
                            </div>
                            <h4>Your inbox is empty</h4>
                            <p class="text-muted">You don't have any messages yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 40px;"></th>
                                        <th>Sender</th>
                                        <th>Subject</th>
                                        <th>Property</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($messages as $message): ?>
                                        <tr class="<?php echo !$message['read_status'] ? 'table-active fw-bold' : ''; ?>">
                                            <td class="text-center">
                                                <?php if (!$message['read_status']): ?>
                                                    <span class="text-primary"><i class="fas fa-envelope"></i></span>
                                                <?php else: ?>
                                                    <span class="text-muted"><i class="fas fa-envelope-open"></i></span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $message['sender_name']; ?></td>
                                            <td>
                                                <a href="view_message.php?id=<?php echo $message['id']; ?>" class="text-decoration-none text-dark">
                                                    <?php echo $message['subject']; ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php if ($message['property_id']): ?>
                                                    <a href="property.php?id=<?php echo $message['property_id']; ?>" class="text-decoration-none">
                                                        <?php echo $message['property_title']; ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('M d, Y H:i', strtotime($message['created_at'])); ?></td>
                                            <td>
                                                <a href="view_message.php?id=<?php echo $message['id']; ?>" class="btn btn-sm btn-outline-primary me-1">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="reply_message.php?id=<?php echo $message['id']; ?>" class="btn btn-sm btn-outline-secondary me-1">
                                                    <i class="fas fa-reply"></i>
                                                </a>
                                                <a href="inbox.php?delete=<?php echo $message['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this message?');">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?> 