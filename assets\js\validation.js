/**
 * Client-Side Form Validation for TX Properties
 * Provides real-time validation feedback to users
 */

// Validation patterns
const validationPatterns = {
    name: /^[a-zA-Z\s'\-]+$/,
    username: /^[a-zA-Z][a-zA-Z0-9._]*$/,
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    phone: /^(\+\d{1,3})?\d{10,15}$/,
    propertyTitle: /^[a-zA-Z0-9\s.,!?\-'"()&]+$/,
    price: /^\d+([.,]\d{3})*([.,]\d{2})?$/
};

// Validation messages
const validationMessages = {
    name: {
        required: 'Name is required',
        invalid: 'Name can only contain letters, spaces, apostrophes, and hyphens',
        minLength: 'Name must be at least 2 characters long',
        maxLength: 'Name must not exceed 100 characters'
    },
    username: {
        required: 'Username is required',
        invalid: 'Username can only contain letters, numbers, underscores, and dots',
        minLength: 'Username must be at least 3 characters long',
        maxLength: 'Username must not exceed 30 characters',
        startLetter: 'Username must start with a letter'
    },
    email: {
        required: 'Email is required',
        invalid: 'Please enter a valid email address'
    },
    phone: {
        invalid: 'Please enter a valid phone number (10-15 digits, optional country code)'
    },
    propertyTitle: {
        required: 'Property title is required',
        invalid: 'Property title contains invalid characters',
        minLength: 'Property title must be at least 5 characters long',
        maxLength: 'Property title must not exceed 200 characters'
    },
    price: {
        required: 'Price is required',
        invalid: 'Please enter a valid price',
        minValue: 'Price must be greater than 0'
    },
    description: {
        required: 'Description is required',
        minLength: 'Description must be at least 10 characters long',
        maxLength: 'Description must not exceed 5000 characters'
    },
    password: {
        required: 'Password is required',
        minLength: 'Password must be at least 8 characters long',
        uppercase: 'Password must contain at least one uppercase letter',
        lowercase: 'Password must contain at least one lowercase letter',
        number: 'Password must contain at least one number'
    }
};

/**
 * Validate name field
 */
function validateName(value, fieldName = 'name') {
    const errors = [];
    
    if (!value.trim()) {
        errors.push(validationMessages.name.required);
        return errors;
    }
    
    if (value.length < 2) {
        errors.push(validationMessages.name.minLength);
    }
    
    if (value.length > 100) {
        errors.push(validationMessages.name.maxLength);
    }
    
    if (!validationPatterns.name.test(value)) {
        errors.push(validationMessages.name.invalid);
    }
    
    return errors;
}

/**
 * Validate username field
 */
function validateUsername(value) {
    const errors = [];
    
    if (!value.trim()) {
        errors.push(validationMessages.username.required);
        return errors;
    }
    
    if (value.length < 3) {
        errors.push(validationMessages.username.minLength);
    }
    
    if (value.length > 30) {
        errors.push(validationMessages.username.maxLength);
    }
    
    if (!/^[a-zA-Z]/.test(value)) {
        errors.push(validationMessages.username.startLetter);
    }
    
    if (!validationPatterns.username.test(value)) {
        errors.push(validationMessages.username.invalid);
    }
    
    return errors;
}

/**
 * Validate email field
 */
function validateEmail(value) {
    const errors = [];
    
    if (!value.trim()) {
        errors.push(validationMessages.email.required);
        return errors;
    }
    
    if (!validationPatterns.email.test(value)) {
        errors.push(validationMessages.email.invalid);
    }
    
    return errors;
}

/**
 * Validate phone field
 */
function validatePhone(value, required = false) {
    const errors = [];
    
    if (!value.trim()) {
        if (required) {
            errors.push('Phone number is required');
        }
        return errors;
    }
    
    // Clean phone number
    const cleanPhone = value.replace(/[^\d+]/g, '');
    
    if (!validationPatterns.phone.test(cleanPhone)) {
        errors.push(validationMessages.phone.invalid);
    }
    
    return errors;
}

/**
 * Validate property title
 */
function validatePropertyTitle(value) {
    const errors = [];
    
    if (!value.trim()) {
        errors.push(validationMessages.propertyTitle.required);
        return errors;
    }
    
    if (value.length < 5) {
        errors.push(validationMessages.propertyTitle.minLength);
    }
    
    if (value.length > 200) {
        errors.push(validationMessages.propertyTitle.maxLength);
    }
    
    if (!validationPatterns.propertyTitle.test(value)) {
        errors.push(validationMessages.propertyTitle.invalid);
    }
    
    return errors;
}

/**
 * Validate price field
 */
function validatePrice(value) {
    const errors = [];
    
    if (!value.trim()) {
        errors.push(validationMessages.price.required);
        return errors;
    }
    
    // Clean price (remove currency symbols and formatting)
    const cleanPrice = value.replace(/[^\d.,]/g, '');
    const numericPrice = parseFloat(cleanPrice.replace(/,/g, ''));
    
    if (isNaN(numericPrice) || numericPrice <= 0) {
        errors.push(validationMessages.price.invalid);
    }
    
    return errors;
}

/**
 * Validate description field
 */
function validateDescription(value, minLength = 10, maxLength = 5000) {
    const errors = [];
    
    if (!value.trim()) {
        errors.push(validationMessages.description.required);
        return errors;
    }
    
    if (value.length < minLength) {
        errors.push(`Description must be at least ${minLength} characters long`);
    }
    
    if (value.length > maxLength) {
        errors.push(`Description must not exceed ${maxLength} characters`);
    }
    
    return errors;
}

/**
 * Validate password field
 */
function validatePassword(value) {
    const errors = [];
    
    if (!value) {
        errors.push(validationMessages.password.required);
        return errors;
    }
    
    if (value.length < 8) {
        errors.push(validationMessages.password.minLength);
    }
    
    if (!/[A-Z]/.test(value)) {
        errors.push(validationMessages.password.uppercase);
    }
    
    if (!/[a-z]/.test(value)) {
        errors.push(validationMessages.password.lowercase);
    }
    
    if (!/\d/.test(value)) {
        errors.push(validationMessages.password.number);
    }
    
    return errors;
}

/**
 * Display validation errors for a field
 */
function displayFieldErrors(fieldId, errors, showSuccess = true) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    const errorContainer = document.getElementById(fieldId + '_error') || createErrorContainer(fieldId);

    if (errors.length > 0) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        errorContainer.innerHTML = errors.join('<br>');
        errorContainer.style.display = 'block';
    } else if (showSuccess && field.value.trim() !== '') {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        errorContainer.style.display = 'none';
    } else {
        field.classList.remove('is-invalid', 'is-valid');
        errorContainer.style.display = 'none';
    }
}

/**
 * Create error container for a field
 */
function createErrorContainer(fieldId) {
    const field = document.getElementById(fieldId);
    const errorContainer = document.createElement('div');
    errorContainer.id = fieldId + '_error';
    errorContainer.className = 'invalid-feedback d-block';
    errorContainer.style.display = 'none';
    
    field.parentNode.appendChild(errorContainer);
    return errorContainer;
}

/**
 * Clear all validation states from a field
 */
function clearFieldValidation(field) {
    field.classList.remove('is-valid', 'is-invalid');
    const errorContainer = document.getElementById(field.id + '_error');
    if (errorContainer) {
        errorContainer.style.display = 'none';
    }
}

/**
 * Initialize real-time validation for forms
 */
function initializeValidation() {
    // Name fields
    const nameFields = document.querySelectorAll('input[name="name"], input[name="full_name"]');
    nameFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validateName(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validateName(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    });
    
    // Username field
    const usernameField = document.getElementById('username');
    if (usernameField) {
        usernameField.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validateUsername(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        usernameField.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validateUsername(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    }
    
    // Email fields
    const emailFields = document.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validateEmail(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validateEmail(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    });
    
    // Phone fields
    const phoneFields = document.querySelectorAll('input[name="phone"]');
    phoneFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validatePhone(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validatePhone(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    });

    // Property title field
    const titleField = document.getElementById('title');
    if (titleField) {
        titleField.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validatePropertyTitle(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        titleField.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validatePropertyTitle(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    }

    // Price fields
    const priceFields = document.querySelectorAll('input[name="price"]');
    priceFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validatePrice(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validatePrice(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    });

    // Description fields
    const descriptionFields = document.querySelectorAll('textarea[name="description"]');
    descriptionFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                const errors = validateDescription(this.value);
                displayFieldErrors(this.id, errors);
            }
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                const errors = validateDescription(this.value);
                displayFieldErrors(this.id, errors);
            }
        });
    });

    // Password fields
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(field => {
        if (field.name === 'password') {
            field.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    const errors = validatePassword(this.value);
                    displayFieldErrors(this.id, errors);
                }
            });

            field.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                    const errors = validatePassword(this.value);
                    displayFieldErrors(this.id, errors);
                }
            });
        }
    });

}

// Initialize validation when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeValidation);
