<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/rental_functions.php';
require_once 'includes/flutterwave_config.php';

// Enhanced Flutterwave verification for rental payments
$paymentVerified = false;
$verificationError = '';

if (isset($_GET['transaction_id'])) {
    $transactionId = $_GET['transaction_id'];
    $propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;
    $tenantId = isset($_GET['tenant_id']) ? (int)$_GET['tenant_id'] : 0;
    $landlordId = isset($_GET['landlord_id']) ? (int)$_GET['landlord_id'] : 0;
    $monthlyRate = isset($_GET['monthly_rate']) ? floatval($_GET['monthly_rate']) : 0;
    $duration = isset($_GET['duration']) ? (int)$_GET['duration'] : 0;
    $totalAmount = isset($_GET['total_amount']) ? floatval($_GET['total_amount']) : 0;
    $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d');
    $landlordName = isset($_GET['landlord_name']) ? $_GET['landlord_name'] : '';

    // Verify transaction with Flutterwave API using new configuration
    $verificationResult = verifyFlutterwaveTransaction($transactionId);
    
    if ($verificationResult === false) {
        $verificationError = "Failed to verify transaction with Flutterwave API";
        // For development: Accept payment even with API verification issues
        $paymentVerified = true;
    } else {
        $isSuccess = isFlutterwaveTransactionSuccessful($verificationResult);
        
        if ($isSuccess) {
            $paymentVerified = true;
            error_log("Rental payment verification successful for transaction: $transactionId");
        } else {
            $verificationError = "Rental payment verification failed - transaction not successful";
            // For development: Accept payment even with verification issues
            $paymentVerified = true;
            error_log("Rental payment verification failed for transaction: $transactionId");
        }
    }
}

// If verification failed, show error page but allow continuation
if (isset($_GET['transaction_id']) && !$paymentVerified) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Rental Payment Verification</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            <h3 class="text-warning mt-3">Rental Payment Verification Issue</h3>
                            <p class="text-muted"><?php echo htmlspecialchars($verificationError); ?></p>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>What happened?</h6>
                                <p class="mb-0">We couldn't automatically verify your rental payment with Flutterwave, but this doesn't mean your payment failed.</p>
                            </div>
                            
                            <div class="d-flex gap-3 justify-content-center">
                                <a href="rental_payment_success.php?transaction_id=<?php echo urlencode($transactionId); ?>&property_id=<?php echo $propertyId; ?>&tenant_id=<?php echo $tenantId; ?>&landlord_id=<?php echo $landlordId; ?>&monthly_rate=<?php echo $monthlyRate; ?>&duration=<?php echo $duration; ?>&total_amount=<?php echo $totalAmount; ?>&start_date=<?php echo $startDate; ?>&landlord_name=<?php echo urlencode($landlordName); ?>" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i>Continue - Payment Successful
                                </a>
                                <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Property
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Check if user is logged in and is a buyer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    die('Not authorized.');
}

// Validate required parameters
if (!$propertyId || !$tenantId || !$landlordId || !$monthlyRate || !$duration || !$totalAmount) {
    die('Missing required rental payment parameters');
}

// Get property details
$property = getPropertyById($propertyId);
if (!$property || $property['listing_type'] !== 'Rent') {
    die('Invalid property for rental');
}

$propertyTitle = $property['title'];
$tenantName = $_SESSION['username'];

// Calculate rental end date
$endDate = calculateRentalEndDate($startDate, $duration);

try {
    // Process rental payment
    $rentalProcessed = processRentalPayment($propertyId, $tenantId, $landlordId, $monthlyRate, $duration, $totalAmount, $transactionId, $startDate);
    
    if (!$rentalProcessed) {
        throw new Exception("Failed to process rental payment");
    }
    
    // Send rental confirmation messages
    $rentalDetails = [
        'monthly_rate' => $monthlyRate,
        'duration' => $duration,
        'total_amount' => $totalAmount,
        'start_date' => $startDate,
        'end_date' => $endDate,
        'transaction_id' => $transactionId
    ];
    
    $messagesSent = sendRentalConfirmationMessages($propertyId, $tenantId, $landlordId, $rentalDetails);
    
    if (!$messagesSent) {
        error_log("Failed to send rental confirmation messages for transaction: $transactionId");
    }
    
    // Success! Show success page
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Rental Payment Successful</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card shadow-lg">
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h2 class="text-success mb-3">Rental Payment Successful!</h2>
                            <h4 class="mb-4">🏠 Congratulations on your new rental!</h4>
                            
                            <div class="alert alert-success">
                                <h5 class="mb-3">Rental Agreement Details:</h5>
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Property:</strong></div>
                                            <div class="col-sm-7"><?php echo htmlspecialchars($propertyTitle); ?></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Monthly Rate:</strong></div>
                                            <div class="col-sm-7">TZS <?php echo number_format($monthlyRate, 2); ?></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Duration:</strong></div>
                                            <div class="col-sm-7"><?php echo $duration; ?> months</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Total Paid:</strong></div>
                                            <div class="col-sm-7 text-success"><strong>TZS <?php echo number_format($totalAmount, 2); ?></strong></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Start Date:</strong></div>
                                            <div class="col-sm-7"><?php echo date('M j, Y', strtotime($startDate)); ?></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>End Date:</strong></div>
                                            <div class="col-sm-7"><?php echo date('M j, Y', strtotime($endDate)); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-start">
                                    <div class="col-sm-4"><strong>Transaction ID:</strong></div>
                                    <div class="col-sm-8"><code><?php echo $transactionId; ?></code></div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>What happens next?</h6>
                                <ul class="text-start mb-0">
                                    <li>The property is now marked as <strong>RENTED</strong> for your rental period</li>
                                    <li>You and the landlord have received confirmation messages</li>
                                    <li>The landlord will contact you for key handover and move-in procedures</li>
                                    <li>Check your inbox for detailed rental agreement confirmation</li>
                                    <li>Your rental period starts on <strong><?php echo date('M j, Y', strtotime($startDate)); ?></strong></li>
                                </ul>
                            </div>
                            
                            <div class="d-flex gap-3 justify-content-center flex-wrap mt-4">
                                <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>View Property
                                </a>
                                <a href="my_rentals.php" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>My Rentals
                                </a>
                                <a href="inbox.php" class="btn btn-info">
                                    <i class="fas fa-envelope me-2"></i>Check Messages
                                </a>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-search me-2"></i>Browse More Properties
                                </a>
                            </div>
                            
                            <p class="text-muted mt-4">This window will close automatically in <span id="countdown">10</span> seconds.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            let countdown = 10;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(function() {
                countdown--;
                countdownElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    if (window.opener) {
                        window.opener.location.href = 'my_rentals.php?rental_success=1&property_id=<?php echo $propertyId; ?>';
                    }
                    window.close();
                }
            }, 1000);
        </script>
    </body>
    </html>
    <?php
    
} catch (Exception $e) {
    // Error handling
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Rental Payment Error</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-times-circle text-danger" style="font-size: 3rem;"></i>
                            <h3 class="text-danger mt-3">Rental Payment Processing Error</h3>
                            <p class="text-muted"><?php echo htmlspecialchars($e->getMessage()); ?></p>
                            <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-primary">Back to Property</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

$conn->close();
?>
