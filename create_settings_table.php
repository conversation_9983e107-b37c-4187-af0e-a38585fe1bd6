<?php
require_once 'includes/config.php';

// Create settings table if it does not exist
$sql = "CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if ($conn->query($sql) === TRUE) {
    echo "Settings table created or already exists.<br>";
    // Check if table is empty
    $result = $conn->query("SELECT COUNT(*) as count FROM settings");
    $row = $result->fetch_assoc();
    if ($row['count'] == 0) {
        // Insert default row
        $site_name = 'TX Properties';
        $contact_email = '<EMAIL>';
        $stmt = $conn->prepare("INSERT INTO settings (site_name, contact_email) VALUES (?, ?)");
        $stmt->bind_param('ss', $site_name, $contact_email);
        if ($stmt->execute()) {
            echo "Default settings row inserted.<br>";
        } else {
            echo "Failed to insert default row: " . $conn->error . "<br>";
        }
    } else {
        echo "Settings table already has data.<br>";
    }
} else {
    echo "Error creating table: " . $conn->error . "<br>";
} 