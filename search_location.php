<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Define Tanzania regions
$regions = [
    'Arusha',
    'Dar es Salaam',
    'Dodoma',
    'Mbeya',
    'Tanga',
    'Zanzibar'
];

// Count properties for each region
$regionCounts = [];
foreach ($regions as $region) {
    $criteria = ['location' => $region];
    $regionProps = searchProperties($criteria);
    $regionCounts[$region] = count($regionProps);
}
?>

<!-- Page Header -->
<div class="container">
    <div class="row my-4">
        <div class="col-12">
            <h1>Search by Region</h1>
            <p class="text-muted">Find properties across Tanzania's major regions</p>
        </div>
    </div>
</div>

<!-- Regions as Property Cards -->
<div class="container mb-5">
    <h2 class="section-title">Browse Properties by Region</h2>
    
    <div class="row">
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card property-card h-100">
                <div class="position-relative">
                    <img src="assets/images/dar-es-salaam.jpg" class="card-img-top property-img" alt="Dar es Salaam" style="height: 220px; object-fit: cover;">
                    <span class="badge position-absolute top-0 end-0 m-2 bg-primary">
                        <?php echo $regionCounts['Dar es Salaam']; ?> Properties
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="property-title">Dar es Salaam</h5>
                    <p class="card-text">Tanzania's largest city and business hub with prime residential and commercial properties.</p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt"></i> Business Capital</p>
                </div>
                <div class="card-footer bg-white">
                    <a href="search.php?location=Dar es Salaam" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>View Properties
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card property-card h-100">
                <div class="position-relative">
                    <img src="assets/images/arusha.jpg" class="card-img-top property-img" alt="Arusha" style="height: 220px; object-fit: cover;">
                    <span class="badge position-absolute top-0 end-0 m-2 bg-primary">
                        <?php echo $regionCounts['Arusha']; ?> Properties
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="property-title">Arusha</h5>
                    <p class="card-text">Gateway to safari destinations, offering a mix of urban and countryside properties.</p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt"></i> Safari Gateway</p>
                </div>
                <div class="card-footer bg-white">
                    <a href="search.php?location=Arusha" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>View Properties
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card property-card h-100">
                <div class="position-relative">
                    <img src="assets/images/zanzibar.jpg" class="card-img-top property-img" alt="Zanzibar" style="height: 220px; object-fit: cover;">
                    <span class="badge position-absolute top-0 end-0 m-2 bg-primary">
                        <?php echo $regionCounts['Zanzibar']; ?> Properties
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="property-title">Zanzibar</h5>
                    <p class="card-text">Exotic island paradise with beachfront villas and investment opportunities.</p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt"></i> Island Paradise</p>
                </div>
                <div class="card-footer bg-white">
                    <a href="search.php?location=Zanzibar" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>View Properties
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card property-card h-100">
                <div class="position-relative">
                    <img src="assets/images/dodoma.jpg" class="card-img-top property-img" alt="Dodoma" style="height: 220px; object-fit: cover;">
                    <span class="badge position-absolute top-0 end-0 m-2 bg-primary">
                        <?php echo $regionCounts['Dodoma']; ?> Properties
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="property-title">Dodoma</h5>
                    <p class="card-text">Tanzania's capital city with growing real estate developments and opportunities.</p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt"></i> Capital City</p>
                </div>
                <div class="card-footer bg-white">
                    <a href="search.php?location=Dodoma" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>View Properties
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card property-card h-100">
                <div class="position-relative">
                    <img src="assets/images/tanga.jpg" class="card-img-top property-img" alt="Tanga" style="height: 220px; object-fit: cover;">
                    <span class="badge position-absolute top-0 end-0 m-2 bg-primary">
                        <?php echo $regionCounts['Tanga']; ?> Properties
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="property-title">Tanga</h5>
                    <p class="card-text">Coastal region with historical significance and emerging property market.</p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt"></i> Coastal Region</p>
                </div>
                <div class="card-footer bg-white">
                    <a href="search.php?location=Tanga" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>View Properties
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card property-card h-100">
                <div class="position-relative">
                    <img src="assets/images/mbeya.jpg" class="card-img-top property-img" alt="Mbeya" style="height: 220px; object-fit: cover;">
                    <span class="badge position-absolute top-0 end-0 m-2 bg-primary">
                        <?php echo $regionCounts['Mbeya']; ?> Properties
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="property-title">Mbeya</h5>
                    <p class="card-text">Southern highlands region known for its pleasant climate and agricultural opportunities.</p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt"></i> Southern Highlands</p>
                </div>
                <div class="card-footer bg-white">
                    <a href="search.php?location=Mbeya" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>View Properties
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Location Guide Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">Tanzania Property Location Guide</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Urban Centers</h5>
                        <p class="card-text">Tanzania's urban centers like Dar es Salaam, Arusha, and Mwanza offer a mix of apartments, houses, and commercial properties. These areas are ideal for those seeking modern amenities and business opportunities.</p>
                        <p class="card-text">Property values in urban centers have been steadily increasing, making them excellent investment opportunities.</p>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Coastal Regions</h5>
                        <p class="card-text">The coastal regions of Tanzania, including Zanzibar, Bagamoyo, and Tanga, offer beautiful beachfront properties and vacation homes. These areas are popular among tourists and investors looking for rental income.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Northern Highlands</h5>
                        <p class="card-text">The northern highlands, including Arusha and Moshi, offer cooler climates and stunning views of Mount Kilimanjaro and Mount Meru. Properties here range from coffee plantations to luxury homes.</p>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Emerging Areas</h5>
                        <p class="card-text">Areas like Dodoma (the capital city) and Morogoro are seeing significant development and infrastructure improvements, making them attractive for long-term investment in land and residential properties.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
require_once 'includes/footer.php';
?> 