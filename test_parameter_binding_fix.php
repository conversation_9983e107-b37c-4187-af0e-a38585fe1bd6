<?php
// Test Parameter Binding Fix - Verify SQL parameter binding is correct
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Parameter Binding Fix</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔧 Parameter Binding Fix Test</h1>";

// Step 1: Error Fixed
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-danger text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-bug me-2'></i>Error Fixed</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>❌ Original Error:</h5>";
echo "<div class='alert alert-danger'>";
echo "<strong>Fatal error: ArgumentCountError</strong><br>";
echo "<code>The number of elements in the type definition string must match the number of bind variables</code><br><br>";
echo "<strong>Location:</strong> simple_payment_success.php:35<br>";
echo "<strong>Cause:</strong> Mismatch between SQL parameters and bind_param call";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>✅ Fix Applied:</h5>";
echo "<div class='alert alert-success'>";
echo "<strong>Parameter Binding Corrected</strong><br>";
echo "• SQL query has 5 parameters (?)<br>";
echo "• bind_param now has 5 values<br>";
echo "• Type string matches parameter count<br>";
echo "• Correct data types specified";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h6>Technical Details:</h6>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<strong>SQL Query:</strong>";
echo "<pre class='bg-light p-2 mt-2'>";
echo "INSERT INTO payments (\n";
echo "  property_id,     -- ?\n";
echo "  buyer_id,        -- ?\n";
echo "  seller_id,       -- ?\n";
echo "  amount,          -- ?\n";
echo "  date,            -- NOW()\n";
echo "  status,          -- 'completed'\n";
echo "  transaction_id,  -- ?\n";
echo "  payment_method   -- 'Flutterwave'\n";
echo ") VALUES (?, ?, ?, ?, NOW(), 'completed', ?, 'Flutterwave')";
echo "</pre>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<strong>Parameter Binding:</strong>";
echo "<pre class='bg-light p-2 mt-2'>";
echo "bind_param('iiids', \n";
echo "  \$propertyId,    -- i (integer)\n";
echo "  \$buyerId,       -- i (integer)\n";
echo "  \$sellerId,      -- i (integer)\n";
echo "  \$amount,        -- d (double/float)\n";
echo "  \$transactionId  -- s (string)\n";
echo ")";
echo "</pre>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Step 2: Test Available Properties
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-home me-2'></i>Test Properties</h3>";
echo "</div>";
echo "<div class='card-body'>";

$availableProperties = $conn->query("SELECT id, title, status, price FROM properties WHERE status = 'Available' ORDER BY id LIMIT 3")->fetch_all(MYSQLI_ASSOC);

if (empty($availableProperties)) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>No available properties found. Let me create a test property...";
    echo "</div>";
    
    // Create a test property
    $testTitle = "Test Property for Parameter Fix - " . date('Y-m-d H:i:s');
    $testPrice = 175000;
    $testSellerId = 1;
    
    $sql = "INSERT INTO properties (title, description, price, location, property_type, listing_type, bedrooms, bathrooms, area, seller_id, status) 
            VALUES (?, 'Test property for parameter binding fix verification', ?, 'Dar es Salaam', 'House', 'Sale', 3, 2, 120.5, ?, 'Available')";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sdi', $testTitle, $testPrice, $testSellerId);
    
    if ($stmt->execute()) {
        $newPropertyId = $conn->insert_id;
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check me-2'></i>Created test property #$newPropertyId: $testTitle";
        echo "</div>";
        
        // Refresh the available properties
        $availableProperties = $conn->query("SELECT id, title, status, price FROM properties WHERE status = 'Available' ORDER BY id LIMIT 3")->fetch_all(MYSQLI_ASSOC);
    }
}

if (!empty($availableProperties)) {
    echo "<div class='row'>";
    foreach ($availableProperties as $prop) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>Property #{$prop['id']}</h6>";
        echo "<p class='card-text'>" . htmlspecialchars(substr($prop['title'], 0, 30)) . "...</p>";
        echo "<p class='text-success'><strong>TZS " . number_format($prop['price']) . "</strong></p>";
        echo "<span class='badge bg-success mb-2'>{$prop['status']}</span>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='property.php?id={$prop['id']}' class='btn btn-primary btn-sm' target='_blank'>View Property</a>";
        
        // Test the fixed payment processor
        $sellerId = 1;
        $sellerName = "Test Seller";
        $testTxnId = "FIXED_" . time() . "_" . $prop['id'];
        
        echo "<a href='simple_payment_success.php?transaction_id=$testTxnId&property_id={$prop['id']}&amount={$prop['price']}&seller_id=$sellerId&seller_name=" . urlencode($sellerName) . "' class='btn btn-success btn-sm' target='_blank'>Test Fixed Payment</a>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div></div>";

// Step 3: Testing Instructions
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-clipboard-list me-2'></i>Testing Instructions</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>🧪 How to Test the Fix:</h5>";
echo "<ol>";
echo "<li><strong>Click 'Test Fixed Payment'</strong> on any property above</li>";
echo "<li><strong>Verify No Errors</strong> - should not see ArgumentCountError</li>";
echo "<li><strong>Check Success Page</strong> - should display payment confirmation</li>";
echo "<li><strong>Verify Database Updates</strong> - property should be marked as sold</li>";
echo "<li><strong>Check Listing Removal</strong> - property should disappear from homepage</li>";
echo "</ol>";

echo "<div class='alert alert-info'>";
echo "<h6><i class='fas fa-info-circle me-2'></i>Expected Results:</h6>";
echo "<ul class='mb-0'>";
echo "<li>✅ No fatal errors or parameter binding issues</li>";
echo "<li>✅ Payment record created successfully in database</li>";
echo "<li>✅ Property status updated to 'Sold'</li>";
echo "<li>✅ Messages sent to buyer and seller</li>";
echo "<li>✅ Success page displays with payment details</li>";
echo "<li>✅ Property removed from public listings</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

// Step 4: Database Status
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Status</h3>";
echo "</div>";
echo "<div class='card-body'>";

$totalProperties = $conn->query("SELECT COUNT(*) as count FROM properties")->fetch_assoc()['count'];
$availableCount = $conn->query("SELECT COUNT(*) as count FROM properties WHERE status = 'Available'")->fetch_assoc()['count'];
$soldCount = $conn->query("SELECT COUNT(*) as count FROM properties WHERE status = 'Sold'")->fetch_assoc()['count'];
$totalPayments = $conn->query("SELECT COUNT(*) as count FROM payments WHERE status = 'completed'")->fetch_assoc()['count'];

echo "<div class='row text-center'>";
echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-primary'>$totalProperties</h3>";
echo "<p class='mb-0'>Total Properties</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-success'>$availableCount</h3>";
echo "<p class='mb-0'>Available</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-danger'>$soldCount</h3>";
echo "<p class='mb-0'>Sold</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-info'>$totalPayments</h3>";
echo "<p class='mb-0'>Payments</p>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Action Buttons
echo "<div class='text-center'>";
echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='index.php' class='btn btn-primary' target='_blank'><i class='fas fa-home me-2'></i>Homepage</a>";
echo "<a href='test_flutterwave_api_keys.php' class='btn btn-info' target='_blank'><i class='fas fa-key me-2'></i>API Keys Test</a>";
echo "<a href='my_paid_properties.php' class='btn btn-warning' target='_blank'><i class='fas fa-list me-2'></i>My Purchases</a>";
echo "<a href='login.php' class='btn btn-success'><i class='fas fa-sign-in-alt me-2'></i>Login</a>";
echo "</div>";
echo "</div>";

// Final Status
echo "<div class='text-center mt-4'>";
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>✅ PARAMETER BINDING ERROR FIXED!</h4>";
echo "<p class='mb-0'>SQL parameter binding corrected - payment processing should now work without errors.</p>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
