<?php
// Database connection parameters
$host = 'localhost';
$user = 'root';
$password = 'password'; // Update this if your MySQL password is different
$database = 'txproperties';

// Create connection
$conn = new mysqli($host, $user, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Database Update Script</h1>";

// SQL commands from update_database.sql
$sql = [
    // Add local_area column to properties table
    "ALTER TABLE properties ADD COLUMN local_area VARCHAR(100) AFTER location",

    // Add property features columns
    "ALTER TABLE properties ADD COLUMN has_electricity BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_gym BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_air_condition BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_security BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_pool BOOLEAN DEFAULT FALSE",

    // Update property_type ENUM to include new types
    "ALTER TABLE properties MODIFY COLUMN property_type ENUM('Land', 'House', 'Apartment', 'Commercial', 'Office', 'Office Space', 'Store/Shop')"
];

// Execute each SQL command
foreach ($sql as $query) {
    echo "<p>Executing: " . htmlspecialchars($query) . "</p>";
    
    try {
        if ($conn->query($query)) {
            echo "<p style='color:green'>Success!</p>";
        } else {
            echo "<p style='color:red'>Error: " . $conn->error . "</p>";
        }
    } catch (mysqli_sql_exception $e) {
        // Check if error is because column already exists
        if (strpos($e->getMessage(), "Duplicate column name") !== false) {
            echo "<p style='color:blue'>Column already exists, skipping.</p>";
        } else {
            echo "<p style='color:red'>Error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<hr>";
}

echo "<p><strong>All updates completed!</strong></p>";
echo "<p><a href='add_property.php'>Go to Add Property Page</a></p>";

// Close connection
$conn->close();
?> 