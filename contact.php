<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
include 'includes/header.php';

// Initialize variables
$name = '';
$email = '';
$subject = '';
$message = '';
$error = '';
$success = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    
    // Validate inputs
    if (empty($name)) {
        $error = "Name is required.";
    } elseif (empty($email)) {
        $error = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Please provide a valid email address.";
    } elseif (empty($subject)) {
        $error = "Subject is required.";
    } elseif (empty($message)) {
        $error = "Message is required.";
    } else {
        // Store the message in the database instead of sending email directly
        // This allows you to view messages from your admin panel later
        
        // Create email content
        $email_content = "<p><strong>Name:</strong> " . $name . "</p>";
        $email_content .= "<p><strong>Email:</strong> " . $email . "</p>";
        $email_content .= "<p><strong>Subject:</strong> " . $subject . "</p>";
        $email_content .= "<p><strong>Message:</strong></p>";
        $email_content .= "<p>" . nl2br($message) . "</p>";
        
        // Check if contacts table exists, if not create it
        $check_table = "SHOW TABLES LIKE 'contacts'";
        $table_exists = $conn->query($check_table);
        
        if ($table_exists->num_rows == 0) {
            // Create contacts table
            $create_table = "CREATE TABLE contacts (
                id INT(11) NOT NULL AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                subject VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('unread', 'read') DEFAULT 'unread',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
            
            $conn->query($create_table);
        }
        
        // Insert into database
        $sql = "INSERT INTO contacts (name, email, subject, message) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $name, $email, $subject, $message);
        
        if ($stmt->execute()) {
            $success = "Thank you! Your message has been sent successfully. We will get back to you soon.";
            // Clear form fields
            $name = '';
            $email = '';
            $subject = '';
            $message = '';

            // Send email to admin
            $to = '<EMAIL>';
            $email_subject = "TX Properties Contact Form: $subject";
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: $email\r\n";
            $headers .= "Reply-To: $email\r\n";
            $mailBody = $email_content;
            @mail($to, $email_subject, $mailBody, $headers);
        } else {
            $error = "Sorry, there was an error sending your message. Please try again later.";
        }
    }
}


?>

<!-- Contact Hero Section -->
<div class="bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-4 fw-bold">Contact Us</h1>
                <p class="lead">Get in touch with our team</p>
            </div>
        </div>
    </div>
</div>

<!-- Contact Content -->
<div class="container">
    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-7 mb-5">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h3 class="mb-4">Send Us a Message</h3>
                    
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    
                    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="name" class="form-label">Your Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo $name; ?>" required>
                            <div class="invalid-feedback">
                                Please enter your name.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Your Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $email; ?>" required>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo $subject; ?>" required>
                            <div class="invalid-feedback">
                                Please enter a subject.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="6" required><?php echo $message; ?></textarea>
                            <div class="invalid-feedback">
                                Please enter your message.
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="col-lg-5">
            <div class="card shadow-sm mb-4">
                <div class="card-body p-4">
                    <h3 class="mb-4">Contact Information</h3>
                    
                    <div class="d-flex mb-4">
                        <div class="me-3">
                            <i class="fas fa-map-marker-alt text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Address</h5>
                            <p class="mb-0">Dar es Salaam, Tanzania</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="me-3">
                            <i class="fas fa-phone-alt text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Phone</h5>
                            <p class="mb-0">+255 123 456 789</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="me-3">
                            <i class="fas fa-envelope text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Email</h5>
                            <p class="mb-0"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-clock text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Business Hours</h5>
                            <p class="mb-0">Monday - Friday: 9:00 AM - 5:00 PM<br>
                            Saturday: 10:00 AM - 2:00 PM<br>
                            Sunday: Closed</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h3 class="mb-4">Connect With Us</h3>
                    <div class="d-flex justify-content-between">
                        <a href="#" class="btn btn-outline-primary"><i class="fab fa-facebook-f me-2"></i> Facebook</a>
                        <a href="#" class="btn btn-outline-primary"><i class="fab fa-twitter me-2"></i> Twitter</a>
                        <a href="#" class="btn btn-outline-primary"><i class="fab fa-instagram me-2"></i> Instagram</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Map Section -->
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-5">
                <div class="card-body p-0">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d254901.48244055633!2d39.12546614228709!3d-6.792534370496636!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x185c4c1a2c3baf7b%3A0x6c116257d8080bc1!2sDar%20es%20Salaam%2C%20Tanzania!5e0!3m2!1sen!2sus!4v1655389160642!5m2!1sen!2sus" 
                            width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include JavaScript for form validation -->
<script>
// Form validation script
(function() {
    'use strict';
    
    // Fetch all forms that need validation
    var forms = document.querySelectorAll('.needs-validation');
    
    // Loop over and prevent submission
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
})();
</script>

<?php include 'includes/footer.php'; ?> 