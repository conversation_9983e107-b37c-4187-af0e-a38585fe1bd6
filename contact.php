<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
include 'includes/header.php';

// Initialize variables
$name = '';
$email = '';
$subject = '';
$message = '';
$error = '';
$success = '';
$contactSuccess = false;
$contactError = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_submit'])) {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    $userId = isLoggedIn() ? $_SESSION['user_id'] : null;

    // Insert into contact_messages table
    $sql = "INSERT INTO contact_messages (name, email, subject, message, user_id) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssi", $name, $email, $subject, $message, $userId);
    if ($stmt->execute()) {
        $contactSuccess = true;
    } else {
        $contactError = "Failed to send message. Please try again.";
    }
}

?>

<!-- Contact Hero Section -->
<section class="footer-style-hero">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-4 fw-bold text-white">Contact Us</h1>
                <p class="lead text-white-80">Get in touch with our team</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<div class="container my-5">
    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-7 mb-5">
            <div class="card shadow-lg border-0">
                <div class="card-body p-4">
                    <h3 class="mb-4">Send Us a Message</h3>
                    
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactError)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $contactError; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($contactSuccess): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert" id="successAlert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Message Sent Successfully!</strong><br>
                            Thank you for contacting us. We have received your message and will get back to you as soon as possible.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <script>
                            // Auto-hide success message after 5 seconds
                            setTimeout(function() {
                                const alert = document.getElementById('successAlert');
                                if (alert) {
                                    const bsAlert = new bootstrap.Alert(alert);
                                    bsAlert.close();
                                }
                            }, 5000);
                        </script>
                    <?php endif; ?>
                    
                    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="name" class="form-label">Your Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo $name; ?>" required>
                            <div class="invalid-feedback">
                                Please enter your name.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Your Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $email; ?>" required>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo $subject; ?>" required>
                            <div class="invalid-feedback">
                                Please enter a subject.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="6" required><?php echo $message; ?></textarea>
                            <div class="invalid-feedback">
                                Please enter your message.
                            </div>
                        </div>
                        
                        <button type="submit" name="contact_submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="col-lg-5">
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-body p-4">
                    <h3 class="mb-4">Contact Information</h3>
                    
                    <div class="d-flex mb-4">
                        <div class="me-3">
                            <div class="contact-info-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                        </div>
                        <div>
                            <h5>Address</h5>
                            <p class="mb-0">Dar es Salaam, Tanzania</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="me-3">
                            <div class="contact-info-icon">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                        </div>
                        <div>
                            <h5>Phone</h5>
                            <p class="mb-0">+255 123 456 789</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="me-3">
                            <div class="contact-info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                        <div>
                            <h5>Email</h5>
                            <p class="mb-0"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="d-flex">
                        <div class="me-3">
                            <div class="contact-info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div>
                            <h5>Business Hours</h5>
                            <p class="mb-0">Monday - Friday: 8:00 AM - 6:00 PM<br>
                            Saturday: 9:00 AM - 4:00 PM<br>
                            Sunday: Closed</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Info Card -->
            <div class="card shadow-lg border-0">
                <div class="card-body p-4">
                    <h4 class="mb-3">Why Choose TX Properties?</h4>
                    <ul class="list-unstyled why-choose-list">
                        <li>
                            <div class="why-choose-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Trusted platform in Tanzania</span>
                        </li>
                        <li>
                            <div class="why-choose-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Wide range of properties</span>
                        </li>
                        <li>
                            <div class="why-choose-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Professional support team</span>
                        </li>
                        <li>
                            <div class="why-choose-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>Secure and reliable service</span>
                        </li>
                        <li>
                            <div class="why-choose-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <span>24/7 customer support</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contact CTA Section -->
<section class="footer-style-cta">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h3 class="text-white">Need Immediate Assistance?</h3>
                <p class="mb-4 text-white-80">Our team is here to help you find your perfect property or answer any questions you may have.</p>
                <a href="tel:+255123456789" class="btn btn-light me-3">
                    <i class="fas fa-phone me-2"></i>Call Now
                </a>
                <a href="mailto:<EMAIL>" class="btn btn-outline-light">
                    <i class="fas fa-envelope me-2"></i>Email Us
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?> 