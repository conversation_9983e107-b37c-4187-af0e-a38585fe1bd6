<?php
ob_start();
session_start();
require_once 'includes/config.php';
require_once 'includes/header.php';

if (!isLoggedIn() || $_SESSION['user_role'] !== 'seller') {
    header('Location: login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: seller_dashboard.php');
    exit;
}
$id = (int)$_GET['id'];

// Fetch property and check ownership
$sql = "SELECT * FROM properties WHERE id = ? AND seller_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('ii', $id, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$property = $result->fetch_assoc();
if (!$property) {
    echo '<div class="alert alert-danger">Property not found or you do not have permission to edit this property.</div>';
    require_once 'includes/footer.php';
    ob_end_flush();
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $type = trim($_POST['property_type']);
    $price = floatval($_POST['price']);
    $status = trim($_POST['status']);
    $location = trim($_POST['location']);
    $local_area = trim($_POST['local_area']);
    $sql = "UPDATE properties SET title=?, property_type=?, price=?, status=?, location=?, local_area=? WHERE id=? AND seller_id=?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ssdsisii', $title, $type, $price, $status, $location, $local_area, $id, $_SESSION['user_id']);
    if ($stmt->execute()) {
        header('Location: seller_dashboard.php');
        ob_end_flush();
        exit;
    } else {
        $error = 'Failed to update property.';
    }
}
?>
<div class="container my-4">
    <h1 class="mb-4">Edit Property</h1>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <form method="post" class="mb-4" style="max-width:600px;">
        <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($property['title']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="property_type" class="form-label">Type</label>
            <input type="text" class="form-control" id="property_type" name="property_type" value="<?php echo htmlspecialchars($property['property_type']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="price" class="form-label">Price</label>
            <input type="number" class="form-control" id="price" name="price" value="<?php echo htmlspecialchars($property['price']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status" required>
                <option value="Available" <?php if ($property['status'] == 'Available') echo 'selected'; ?>>Available</option>
                <option value="Sold" <?php if ($property['status'] == 'Sold') echo 'selected'; ?>>Sold</option>
                <option value="Rented" <?php if ($property['status'] == 'Rented') echo 'selected'; ?>>Rented</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="location" class="form-label">Location</label>
            <input type="text" class="form-control" id="location" name="location" value="<?php echo htmlspecialchars($property['location']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="local_area" class="form-label">Local Area</label>
            <input type="text" class="form-control" id="local_area" name="local_area" value="<?php echo htmlspecialchars($property['local_area']); ?>">
        </div>
        <button type="submit" class="btn btn-primary">Save Changes</button>
        <a href="seller_dashboard.php" class="btn btn-secondary">Cancel</a>
    </form>
</div>
<?php require_once 'includes/footer.php'; ob_end_flush(); ?> 