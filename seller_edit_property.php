<?php
ob_start();
session_start();

// Enable strict error reporting for debugging
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

require_once 'includes/config.php';
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in and is a seller
if (!isLoggedIn() || $_SESSION['user_role'] !== 'seller') {
    header('Location: login.php');
    exit;
}

// Validate property ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: seller_dashboard.php');
    exit;
}

$id = (int)$_GET['id'];

// Fetch property and verify ownership
try {
    $sql = "SELECT * FROM properties WHERE id = ? AND seller_id = ?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Database prepare error: " . $conn->error);
    }
    
    $bindResult = $stmt->bind_param('ii', $id, $_SESSION['user_id']);
    if (!$bindResult) {
        throw new Exception("Parameter binding error: " . $stmt->error);
    }
    
    if (!$stmt->execute()) {
        throw new Exception("Query execution error: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $property = $result->fetch_assoc();
    
    if (!$property) {
        echo '<div class="alert alert-danger">Property not found or you do not have permission to edit this property.</div>';
        require_once 'includes/footer.php';
        ob_end_flush();
        exit;
    }
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Database error: ' . $e->getMessage() . '</div>';
    require_once 'includes/footer.php';
    ob_end_flush();
    exit;
}

// Fetch existing images
try {
    $imageSql = "SELECT * FROM property_images WHERE property_id = ? ORDER BY is_primary DESC, id ASC";
    $imageStmt = $conn->prepare($imageSql);
    if (!$imageStmt) {
        throw new Exception("Image query prepare error: " . $conn->error);
    }
    
    $bindResult = $imageStmt->bind_param('i', $id);
    if (!$bindResult) {
        throw new Exception("Image query binding error: " . $imageStmt->error);
    }
    
    if (!$imageStmt->execute()) {
        throw new Exception("Image query execution error: " . $imageStmt->error);
    }
    
    $existingImages = $imageStmt->get_result()->fetch_all(MYSQLI_ASSOC);
} catch (Exception $e) {
    $existingImages = [];
    error_log("Image fetch error: " . $e->getMessage());
}

// Get property count for this seller
try {
    $countSql = "SELECT COUNT(*) as total FROM properties WHERE seller_id = ?";
    $countStmt = $conn->prepare($countSql);
    if (!$countStmt) {
        throw new Exception("Count query prepare error: " . $conn->error);
    }
    
    $bindResult = $countStmt->bind_param('i', $_SESSION['user_id']);
    if (!$bindResult) {
        throw new Exception("Count query binding error: " . $countStmt->error);
    }
    
    if (!$countStmt->execute()) {
        throw new Exception("Count query execution error: " . $countStmt->error);
    }
    
    $propertyCount = $countStmt->get_result()->fetch_assoc()['total'];
} catch (Exception $e) {
    $propertyCount = 0;
    error_log("Count query error: " . $e->getMessage());
}

// Define Tanzania regions
$regions = [
    'Arusha',
    'Dar es Salaam', 
    'Dodoma',
    'Mbeya',
    'Tanga',
    'Zanzibar'
];

// Add this line to get all property types
$propertyTypes = getPropertyTypes();

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get and sanitize form data
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $price = isset($_POST['price']) ? trim($_POST['price']) : '';
    $location = isset($_POST['location']) ? trim($_POST['location']) : '';
    
    // Handle "other" location option
    if ($location === 'other' && isset($_POST['other_location'])) {
        $location = trim($_POST['other_location']);
    }
    
    $localArea = isset($_POST['local_area']) ? trim($_POST['local_area']) : '';
    $propertyType = isset($_POST['property_type']) ? trim($_POST['property_type']) : '';
    $listingType = isset($_POST['listing_type']) ? trim($_POST['listing_type']) : '';
    $bedrooms = isset($_POST['bedrooms']) && !empty($_POST['bedrooms']) ? (int)$_POST['bedrooms'] : 0;
    $bathrooms = isset($_POST['bathrooms']) && !empty($_POST['bathrooms']) ? (int)$_POST['bathrooms'] : 0;
    $area = isset($_POST['area']) && !empty($_POST['area']) ? (float)$_POST['area'] : 0;
    $latitude = isset($_POST['latitude']) && !empty($_POST['latitude']) ? (float)$_POST['latitude'] : -6.776012;
    $longitude = isset($_POST['longitude']) && !empty($_POST['longitude']) ? (float)$_POST['longitude'] : 39.178326;
    $status = isset($_POST['status']) ? trim($_POST['status']) : '';
    
    // Property features
    $hasElectricity = isset($_POST['has_electricity']) ? 1 : 0;
    $hasGym = isset($_POST['has_gym']) ? 1 : 0;
    $hasAirCondition = isset($_POST['has_air_condition']) ? 1 : 0;
    $hasSecurity = isset($_POST['has_security']) ? 1 : 0;
    $hasPool = isset($_POST['has_pool']) ? 1 : 0;
    
    // Validate inputs
    $errors = [];
    
    if (empty($title)) {
        $errors[] = "Property title is required.";
    }
    
    if (empty($description)) {
        $errors[] = "Property description is required.";
    }
    
    if (empty($price)) {
        $errors[] = "Property price is required.";
    } elseif (!is_numeric($price) || $price <= 0) {
        $errors[] = "Price must be a positive number.";
    }
    
    if (empty($location)) {
        $errors[] = "Location is required.";
    }
    
    if (empty($propertyType)) {
        $errors[] = "Property type is required.";
    }
    
    if (empty($listingType)) {
        $errors[] = "Listing type is required.";
    }
    
    // If no validation errors, update property
    if (empty($errors)) {
        try {
            // Main update with basic columns
            $sql = "UPDATE properties SET 
                    title = ?, 
                    description = ?, 
                    price = ?, 
                    location = ?, 
                    property_type = ?, 
                    listing_type = ?, 
                    bedrooms = ?, 
                    bathrooms = ?, 
                    area = ?, 
                    lat = ?, 
                    lng = ?, 
                    status = ?
                    WHERE id = ? AND seller_id = ?";
            
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Failed to prepare main update query: " . $conn->error);
            }
            
            // Ensure proper data types
            $price = (float)$price;
            $bedrooms = (int)$bedrooms;
            $bathrooms = (int)$bathrooms;
            $area = (float)$area;
            $latitude = (float)$latitude;
            $longitude = (float)$longitude;
            
            $bindResult = $stmt->bind_param(
                "ssdsssiiiddsii",
                $title, $description, $price, $location, $propertyType, $listingType,
                $bedrooms, $bathrooms, $area, $latitude, $longitude, $status,
                $id, $_SESSION['user_id']
            );
            
            if (!$bindResult) {
                throw new Exception("Failed to bind main update parameters: " . $stmt->error);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to execute main update: " . $stmt->error);
            }
            
            // Try to update additional columns if they exist
            try {
                $updateSql = "UPDATE properties SET 
                              local_area = ?, 
                              has_electricity = ?, 
                              has_gym = ?, 
                              has_air_condition = ?, 
                              has_security = ?, 
                              has_pool = ?
                              WHERE id = ? AND seller_id = ?";
                
                $updateStmt = $conn->prepare($updateSql);
                if (!$updateStmt) {
                    error_log("Failed to prepare additional features update: " . $conn->error);
                } else {
                    $bindResult = $updateStmt->bind_param(
                        "siiiiiii",
                        $localArea, $hasElectricity, $hasGym, $hasAirCondition, 
                        $hasSecurity, $hasPool, $id, $_SESSION['user_id']
                    );
                    
                    if (!$bindResult) {
                        error_log("Failed to bind additional features parameters: " . $updateStmt->error);
                    } elseif (!$updateStmt->execute()) {
                        error_log("Failed to execute additional features update: " . $updateStmt->error);
                    }
                }
            } catch (Exception $e) {
                error_log("Additional features update error: " . $e->getMessage());
            }
            
            $success = "Property updated successfully!";
            
            // Process new images if uploaded
            if (!empty($_FILES['images']['name'][0])) {
                try {
                    $uploadDir = 'assets/images/properties/' . $id . '/';
                    if (!is_dir($uploadDir)) {
                        if (!mkdir($uploadDir, 0755, true)) {
                            throw new Exception("Failed to create upload directory");
                        }
                    }
                    
                    $uploadedImages = 0;
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
                    
                    foreach ($_FILES['images']['tmp_name'] as $key => $tmp_name) {
                        if ($_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                            $fileName = $_FILES['images']['name'][$key];
                            $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                            
                            // Validate file extension
                            if (!in_array($fileExt, $allowedExtensions)) {
                                continue; // Skip invalid files
                            }
                            
                            // Generate unique filename
                            $newFileName = 'property_' . $id . '_' . time() . '_' . $uploadedImages . '.' . $fileExt;
                            $filePath = $uploadDir . $newFileName;
                            
                            if (move_uploaded_file($tmp_name, $filePath)) {
                                // Insert image record
                                $isPrimary = ($uploadedImages === 0) ? 1 : 0;
                                $imageSql = "INSERT INTO property_images (property_id, image_path, is_primary) VALUES (?, ?, ?)";
                                $imageStmt = $conn->prepare($imageSql);
                                
                                if (!$imageStmt) {
                                    error_log("Failed to prepare image insert: " . $conn->error);
                                } else {
                                    $bindResult = $imageStmt->bind_param('isi', $id, $filePath, $isPrimary);
                                    if (!$bindResult) {
                                        error_log("Failed to bind image insert parameters: " . $imageStmt->error);
                                    } elseif (!$imageStmt->execute()) {
                                        error_log("Failed to execute image insert: " . $imageStmt->error);
                                    } else {
                                        $uploadedImages++;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    error_log("Image upload error: " . $e->getMessage());
                }
            }
            
            // Refresh property data
            try {
                $refreshSql = "SELECT * FROM properties WHERE id = ? AND seller_id = ?";
                $refreshStmt = $conn->prepare($refreshSql);
                if (!$refreshStmt) {
                    throw new Exception("Failed to prepare refresh query: " . $conn->error);
                }
                
                $bindResult = $refreshStmt->bind_param('ii', $id, $_SESSION['user_id']);
                if (!$bindResult) {
                    throw new Exception("Failed to bind refresh parameters: " . $refreshStmt->error);
                }
                
                if (!$refreshStmt->execute()) {
                    throw new Exception("Failed to execute refresh query: " . $refreshStmt->error);
                }
                
                $result = $refreshStmt->get_result();
                $property = $result->fetch_assoc();
            } catch (Exception $e) {
                error_log("Property refresh error: " . $e->getMessage());
            }
            
            // Refresh images
            try {
                $refreshImageSql = "SELECT * FROM property_images WHERE property_id = ? ORDER BY is_primary DESC, id ASC";
                $refreshImageStmt = $conn->prepare($refreshImageSql);
                if (!$refreshImageStmt) {
                    throw new Exception("Failed to prepare image refresh query: " . $conn->error);
                }
                
                $bindResult = $refreshImageStmt->bind_param('i', $id);
                if (!$bindResult) {
                    throw new Exception("Failed to bind image refresh parameters: " . $refreshImageStmt->error);
                }
                
                if (!$refreshImageStmt->execute()) {
                    throw new Exception("Failed to execute image refresh query: " . $refreshImageStmt->error);
                }
                
                $existingImages = $refreshImageStmt->get_result()->fetch_all(MYSQLI_ASSOC);
            } catch (Exception $e) {
                error_log("Image refresh error: " . $e->getMessage());
            }
            
        } catch (Exception $e) {
            $error = 'Error updating property: ' . $e->getMessage();
        }
    } else {
        $error = implode("<br>", $errors);
    }
}
?>

<!-- Page Header -->
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>Edit Property
                    </h1>
                    <p class="text-muted mb-0">Property ID: #<?php echo $id; ?> | Total Properties: <?php echo $propertyCount; ?></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="property.php?id=<?php echo $id; ?>" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-eye me-2"></i>View Property
                    </a>
                    <a href="seller_dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Property Form -->
<div class="container">
    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
        <!-- Basic Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle text-primary me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label for="title" class="form-label">Property Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($property['title']); ?>" required>
                        <div class="invalid-feedback">Please provide a property title.</div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Available" <?php echo $property['status'] == 'Available' ? 'selected' : ''; ?>>Available</option>
                            <option value="Sold" <?php echo $property['status'] == 'Sold' ? 'selected' : ''; ?>>Sold</option>
                            <option value="Rented" <?php echo $property['status'] == 'Rented' ? 'selected' : ''; ?>>Rented</option>
                            <option value="Under Contract" <?php echo $property['status'] == 'Under Contract' ? 'selected' : ''; ?>>Under Contract</option>
                        </select>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($property['description']); ?></textarea>
                    <div class="invalid-feedback">Please provide a property description.</div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="price" class="form-label">Price <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">TZS</span>
                            <input type="number" class="form-control" id="price" name="price" value="<?php echo htmlspecialchars($property['price']); ?>" step="0.01" required>
                        </div>
                        <div class="invalid-feedback">Please provide a valid price.</div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="property_type" class="form-label">Property Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="property_type" name="property_type" required>
                            <option value="">Select Type</option>
                            <?php foreach (
                                $propertyTypes as $type): ?>
                                <option value="<?php echo $type; ?>" <?php echo $property['property_type'] === $type ? 'selected' : ''; ?>><?php echo $type; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Listing Type <span class="text-danger">*</span></label>
                        <div class="d-flex">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="radio" name="listing_type" id="listing_sale" value="Sale" <?php echo $property['listing_type'] === 'Sale' ? 'checked' : ''; ?> required>
                                <label class="form-check-label" for="listing_sale">For Sale</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="listing_type" id="listing_rent" value="Rent" <?php echo $property['listing_type'] === 'Rent' ? 'checked' : ''; ?> required>
                                <label class="form-check-label" for="listing_rent">For Rent</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Property Features -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs text-primary me-2"></i>Property Features
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="bedrooms" class="form-label">Bedrooms</label>
                        <input type="number" class="form-control" id="bedrooms" name="bedrooms" value="<?php echo htmlspecialchars($property['bedrooms'] ?? 0); ?>">
                        <small class="form-text text-muted">Leave empty for land properties.</small>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="bathrooms" class="form-label">Bathrooms</label>
                        <input type="number" class="form-control" id="bathrooms" name="bathrooms" value="<?php echo htmlspecialchars($property['bathrooms'] ?? 0); ?>">
                        <small class="form-text text-muted">Leave empty for land properties.</small>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="area" class="form-label">Area (sqm)</label>
                        <input type="number" step="0.01" class="form-control" id="area" name="area" value="<?php echo htmlspecialchars($property['area'] ?? 0); ?>">
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12 mb-2">
                        <label class="form-label">Additional Features</label>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_electricity" name="has_electricity" value="1" <?php echo isset($property['has_electricity']) && $property['has_electricity'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="has_electricity">
                                <i class="fas fa-bolt text-warning me-2"></i> Electricity
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_gym" name="has_gym" value="1" <?php echo isset($property['has_gym']) && $property['has_gym'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="has_gym">
                                <i class="fas fa-dumbbell text-primary me-2"></i> Gym
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_air_condition" name="has_air_condition" value="1" <?php echo isset($property['has_air_condition']) && $property['has_air_condition'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="has_air_condition">
                                <i class="fas fa-snowflake text-info me-2"></i> Air Condition
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_security" name="has_security" value="1" <?php echo isset($property['has_security']) && $property['has_security'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="has_security">
                                <i class="fas fa-shield-alt text-success me-2"></i> Security
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_pool" name="has_pool" value="1" <?php echo isset($property['has_pool']) && $property['has_pool'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="has_pool">
                                <i class="fas fa-swimming-pool text-primary me-2"></i> Swimming Pool
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Location Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt text-primary me-2"></i>Location Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 mb-3">
                        <label for="location" class="form-label">Region <span class="text-danger">*</span></label>
                        <select class="form-select" id="location" name="location" required>
                            <option value="">Select Region</option>
                            <?php foreach ($regions as $region): ?>
                                <option value="<?php echo $region; ?>" <?php echo $property['location'] === $region ? 'selected' : ''; ?>><?php echo $region; ?></option>
                            <?php endforeach; ?>
                            <option value="other" <?php echo !in_array($property['location'], $regions) ? 'selected' : ''; ?>>Other (Specify)</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="local_area" class="form-label">Local Area</label>
                        <input type="text" class="form-control" id="local_area" name="local_area" value="<?php echo htmlspecialchars($property['local_area'] ?? ''); ?>" placeholder="e.g. Kikuyu, Mwananyamala">
                        <small class="form-text text-muted">Specific area within the region</small>
                    </div>
                    
                    <div class="col-md-4 mb-3" id="otherLocationContainer" style="display: <?php echo !in_array($property['location'], $regions) ? 'block' : 'none'; ?>;">
                        <label for="other_location" class="form-label">Specify Region <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="other_location" name="other_location" value="<?php echo !in_array($property['location'], $regions) ? htmlspecialchars($property['location']) : ''; ?>">
                    </div>
                </div>
                
                <!-- Map for selecting property location -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Property Map Location</label>
                        <p class="text-muted small">Click on the map to set the exact property location or adjust the coordinates manually below.</p>
                        <div id="property-location-map" style="height: 400px;" class="rounded mb-3"></div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="latitude" class="form-label">Latitude</label>
                        <input type="text" class="form-control" id="latitude" name="latitude" value="<?php echo htmlspecialchars($property['lat'] ?? '-6.776012'); ?>">
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="longitude" class="form-label">Longitude</label>
                        <input type="text" class="form-control" id="longitude" name="longitude" value="<?php echo htmlspecialchars($property['lng'] ?? '39.178326'); ?>">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Property Images -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images text-primary me-2"></i>Property Images
                </h5>
            </div>
            <div class="card-body">
                <!-- Existing Images -->
                <?php if (!empty($existingImages)): ?>
                    <div class="mb-4">
                        <h6>Current Images</h6>
                        <div class="row">
                            <?php foreach ($existingImages as $image): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="position-relative">
                                        <img src="<?php echo $image['image_path']; ?>" class="img-fluid rounded" style="height: 150px; width: 100%; object-fit: cover;" alt="Property Image">
                                        <?php if ($image['is_primary']): ?>
                                            <span class="badge bg-primary position-absolute top-0 start-0 m-2">Primary</span>
                                        <?php endif; ?>
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteImage(<?php echo $image['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Upload New Images -->
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="images" class="form-label">Add New Images</label>
                        <input type="file" class="form-control" id="images" name="images[]" accept="image/*" multiple>
                        <small class="form-text text-muted">
                            You can upload multiple images. Max file size: 5MB per image. Supported formats: JPG, PNG, GIF.
                        </small>
                    </div>
                    
                    <div class="col-md-12">
                        <div class="row" id="image_preview_container"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
            <a href="seller_dashboard.php" class="btn btn-outline-secondary me-md-2">
                <i class="fas fa-times me-2"></i>Cancel
            </a>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-save me-2"></i>Save Changes
            </button>
        </div>
    </form>
</div>

<!-- Additional CSS for better form styling -->
<style>
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#property-location-map {
    border: 2px solid #dee2e6;
    border-radius: 8px;
}

.image-preview {
    transition: transform 0.2s ease;
}

.image-preview:hover {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>

<!-- JavaScript for form validation and map functionality -->
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    // Show loading state
                    const submitBtn = document.getElementById('submitBtn');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
                    submitBtn.disabled = true;
                    
                    // Re-enable after 5 seconds if something goes wrong
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 5000);
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Location dropdown handling
document.getElementById('location').addEventListener('change', function() {
    const otherContainer = document.getElementById('otherLocationContainer');
    const otherInput = document.getElementById('other_location');
    
    if (this.value === 'other') {
        otherContainer.style.display = 'block';
        otherInput.required = true;
    } else {
        otherContainer.style.display = 'none';
        otherInput.required = false;
        otherInput.value = '';
    }
});

// Image preview functionality
document.getElementById('images').addEventListener('change', function() {
    const container = document.getElementById('image_preview_container');
    container.innerHTML = '';
    
    for (let i = 0; i < this.files.length; i++) {
        const file = this.files[i];
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const div = document.createElement('div');
            div.className = 'col-md-3 mb-3';
            div.innerHTML = `
                <img src="${e.target.result}" class="img-fluid rounded" style="height: 150px; width: 100%; object-fit: cover;" alt="Preview">
            `;
            container.appendChild(div);
        };
        
        reader.readAsDataURL(file);
    }
});

// Delete image function
function deleteImage(imageId) {
    if (confirm('Are you sure you want to delete this image?')) {
        fetch('delete_property_image.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'image_id=' + imageId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting image: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting image');
        });
    }
}

// Initialize map (if Leaflet is available)
if (typeof L !== 'undefined') {
    const lat = parseFloat(document.getElementById('latitude').value) || -6.776012;
    const lng = parseFloat(document.getElementById('longitude').value) || 39.178326;
    
    const map = L.map('property-location-map').setView([lat, lng], 13);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    let marker = L.marker([lat, lng]).addTo(map);
    
    map.on('click', function(e) {
        marker.setLatLng(e.latlng);
        document.getElementById('latitude').value = e.latlng.lat.toFixed(6);
        document.getElementById('longitude').value = e.latlng.lng.toFixed(6);
    });
    
    // Update marker when coordinates are manually changed
    document.getElementById('latitude').addEventListener('change', updateMarker);
    document.getElementById('longitude').addEventListener('change', updateMarker);
    
    function updateMarker() {
        const lat = parseFloat(document.getElementById('latitude').value);
        const lng = parseFloat(document.getElementById('longitude').value);
        if (!isNaN(lat) && !isNaN(lng)) {
            marker.setLatLng([lat, lng]);
            map.setView([lat, lng]);
        }
    }
}
</script>

<?php require_once 'includes/footer.php'; ob_end_flush(); ?> 