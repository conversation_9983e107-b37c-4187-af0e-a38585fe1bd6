<?php
// Forgot Password - Handle password reset requests
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/email_helper.php';

// Initialize variables
$message = '';
$messageType = '';
$step = 'request'; // request, verify, reset

// Check if user is already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'request_reset':
                handlePasswordResetRequest();
                break;
            case 'verify_token':
                handleTokenVerification();
                break;
            case 'reset_password':
                handlePasswordReset();
                break;
        }
    }
}

// Handle token verification from URL
if (isset($_GET['token']) && isset($_GET['email'])) {
    $step = 'reset';
    $token = $_GET['token'];
    $email = $_GET['email'];
    
    // Verify token is valid and not expired
    $stmt = $conn->prepare("SELECT id, username, reset_token_expires FROM users WHERE email = ? AND reset_token = ? AND reset_token_expires > NOW()");
    $stmt->bind_param("ss", $email, $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $message = 'Invalid or expired reset token. Please request a new password reset.';
        $messageType = 'danger';
        $step = 'request';
    }
}

function handlePasswordResetRequest() {
    global $conn, $message, $messageType, $step;
    
    $email = trim($_POST['email']);
    
    // Validate email
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'danger';
        return;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT id, username, email FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // Don't reveal if email exists or not for security
        $message = 'If an account with that email exists, you will receive a password reset link shortly.';
        $messageType = 'success';
        return;
    }
    
    $user = $result->fetch_assoc();
    
    // Generate reset token
    $resetToken = bin2hex(random_bytes(32));
    $resetExpires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    // Update user with reset token
    $stmt = $conn->prepare("UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE email = ?");
    $stmt->bind_param("sss", $resetToken, $resetExpires, $email);
    
    if ($stmt->execute()) {
        // Send reset email
        $resetLink = BASE_URL . "forgot-password.php?token=" . $resetToken . "&email=" . urlencode($email);
        
        $emailSubject = "Password Reset Request - TX Properties";
        $emailBody = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;'>
            <div style='background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #2c5530; margin: 0;'>TX Properties</h1>
                    <p style='color: #666; margin: 5px 0 0 0;'>Password Reset Request</p>
                </div>
                
                <h2 style='color: #2c5530; margin-bottom: 20px;'>Hello " . htmlspecialchars($user['username']) . ",</h2>
                
                <p style='color: #333; line-height: 1.6; margin-bottom: 20px;'>
                    We received a request to reset your password for your TX Properties account. If you made this request, click the button below to reset your password:
                </p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='" . $resetLink . "' style='background-color: #2c5530; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;'>Reset My Password</a>
                </div>
                
                <p style='color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 15px;'>
                    Or copy and paste this link into your browser:<br>
                    <a href='" . $resetLink . "' style='color: #2c5530; word-break: break-all;'>" . $resetLink . "</a>
                </p>
                
                <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p style='color: #856404; margin: 0; font-size: 14px;'>
                        <strong>Important:</strong> This link will expire in 1 hour for security reasons. If you didn't request this password reset, please ignore this email.
                    </p>
                </div>
                
                <p style='color: #666; font-size: 14px; line-height: 1.6; margin-top: 30px;'>
                    If you're having trouble clicking the button, you can also visit our website and use the 'Forgot Password' feature.
                </p>
                
                <div style='border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        This email was sent from TX Properties. If you have any questions, please contact our support team.
                    </p>
                </div>
            </div>
        </div>";
        
        if (sendEmail($email, $emailSubject, $emailBody)) {
            $message = 'If an account with that email exists, you will receive a password reset link shortly.';
            $messageType = 'success';
        } else {
            $message = 'There was an error sending the reset email. Please try again later.';
            $messageType = 'danger';
        }
    } else {
        $message = 'There was an error processing your request. Please try again later.';
        $messageType = 'danger';
    }
}

function handlePasswordReset() {
    global $conn, $message, $messageType, $step;
    
    $token = $_POST['token'];
    $email = $_POST['email'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Validate passwords
    if (empty($newPassword) || empty($confirmPassword)) {
        $message = 'Please fill in all password fields.';
        $messageType = 'danger';
        return;
    }
    
    if ($newPassword !== $confirmPassword) {
        $message = 'Passwords do not match.';
        $messageType = 'danger';
        return;
    }
    
    if (strlen($newPassword) < 6) {
        $message = 'Password must be at least 6 characters long.';
        $messageType = 'danger';
        return;
    }
    
    // Verify token again
    $stmt = $conn->prepare("SELECT id, username FROM users WHERE email = ? AND reset_token = ? AND reset_token_expires > NOW()");
    $stmt->bind_param("ss", $email, $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $message = 'Invalid or expired reset token. Please request a new password reset.';
        $messageType = 'danger';
        $step = 'request';
        return;
    }
    
    $user = $result->fetch_assoc();
    
    // Hash new password and update user
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    $stmt = $conn->prepare("UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE email = ?");
    $stmt->bind_param("ss", $hashedPassword, $email);
    
    if ($stmt->execute()) {
        $message = 'Your password has been successfully reset. You can now log in with your new password.';
        $messageType = 'success';
        $step = 'complete';
    } else {
        $message = 'There was an error updating your password. Please try again.';
        $messageType = 'danger';
    }
}

require_once 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-key fa-3x text-primary mb-3"></i>
                        <h2 class="card-title text-primary">
                            <?php 
                            switch($step) {
                                case 'request':
                                    echo 'Forgot Password';
                                    break;
                                case 'reset':
                                    echo 'Reset Password';
                                    break;
                                case 'complete':
                                    echo 'Password Reset Complete';
                                    break;
                            }
                            ?>
                        </h2>
                        <p class="text-muted">
                            <?php 
                            switch($step) {
                                case 'request':
                                    echo 'Enter your email address and we\'ll send you a link to reset your password.';
                                    break;
                                case 'reset':
                                    echo 'Enter your new password below.';
                                    break;
                                case 'complete':
                                    echo 'Your password has been successfully reset.';
                                    break;
                            }
                            ?>
                        </p>
                    </div>

                    <?php if (!empty($message)): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($step === 'request'): ?>
                        <!-- Password Reset Request Form -->
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="request_reset">
                            
                            <div class="mb-4">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email" class="form-control form-control-lg" id="email" name="email" 
                                       placeholder="Enter your email address" required>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                                </button>
                            </div>
                        </form>

                    <?php elseif ($step === 'reset'): ?>
                        <!-- Password Reset Form -->
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="reset_password">
                            <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                            <input type="hidden" name="email" value="<?php echo htmlspecialchars($email); ?>">
                            
                            <div class="mb-3">
                                <label for="new_password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>New Password
                                </label>
                                <input type="password" class="form-control form-control-lg" id="new_password" 
                                       name="new_password" placeholder="Enter new password" required minlength="6">
                                <div class="form-text">Password must be at least 6 characters long.</div>
                            </div>

                            <div class="mb-4">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Confirm New Password
                                </label>
                                <input type="password" class="form-control form-control-lg" id="confirm_password" 
                                       name="confirm_password" placeholder="Confirm new password" required minlength="6">
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-check me-2"></i>Reset Password
                                </button>
                            </div>
                        </form>

                    <?php elseif ($step === 'complete'): ?>
                        <!-- Success Message -->
                        <div class="text-center">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle fa-2x mb-3"></i>
                                <h5>Password Reset Successful!</h5>
                                <p class="mb-0">Your password has been successfully updated. You can now log in with your new password.</p>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="login.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="text-muted mb-3">
                            <i class="fas fa-arrow-left me-2"></i>
                            <a href="login.php" class="text-decoration-none">Back to Login</a>
                        </p>
                        
                        <p class="text-muted small">
                            Don't have an account? 
                            <a href="register.php" class="text-decoration-none">Sign up here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    if (newPassword && confirmPassword) {
        function validatePasswords() {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
        
        newPassword.addEventListener('input', validatePasswords);
        confirmPassword.addEventListener('input', validatePasswords);
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
