<?php
/**
 * Browser Test Script for Property Features
 * Access this via: http://localhost/txonline/browser_test.php
 */

require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Property Features Test</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".warning { color: orange; }";
echo "table { border-collapse: collapse; width: 100%; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
echo "th { background-color: #f2f2f2; }";
echo "</style></head><body>";

echo "<h1>🏠 Property Features System Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
if ($conn->ping()) {
    echo "<p class='success'>✅ Database connection successful</p>";
} else {
    echo "<p class='error'>❌ Database connection failed</p>";
    exit;
}

// Test 2: Check Feature Columns
echo "<h2>2. Database Columns Check</h2>";
$columns = ['has_electricity', 'has_water', 'has_fence', 'has_gym', 'has_air_condition', 'has_security', 'has_pool'];
$existingColumns = [];

foreach ($columns as $column) {
    $result = $conn->query("SHOW COLUMNS FROM properties LIKE '$column'");
    if ($result && $result->num_rows > 0) {
        $existingColumns[] = $column;
        echo "<p class='success'>✅ Column '$column' exists</p>";
    } else {
        echo "<p class='error'>❌ Column '$column' is missing</p>";
    }
}

// Calculate totals for later use
$totalColumns = count($columns);
$existingCount = count($existingColumns);

// Test 3: Sample Properties
echo "<h2>3. Sample Properties with Features</h2>";

// Only try to select feature columns if they exist
if ($existingCount === $totalColumns) {
    $sql = "SELECT id, title, has_electricity, has_water, has_fence, has_gym, has_air_condition, has_security, has_pool FROM properties LIMIT 5";
    $result = $conn->query($sql);

    if ($result && $result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Title</th><th>⚡ Electricity</th><th>💧 Water</th><th>🔲 Fence</th><th>🏋️ Gym</th><th>❄️ AC</th><th>🛡️ Security</th><th>🏊 Pool</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['title'], 0, 30)) . "...</td>";
            echo "<td>" . ($row['has_electricity'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['has_water'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['has_fence'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['has_gym'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['has_air_condition'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['has_security'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['has_pool'] ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ No properties found or query failed</p>";
    }
} else {
    echo "<p class='warning'>⚠️ Cannot display properties - feature columns are missing</p>";
    echo "<p class='error'>Missing " . ($totalColumns - $existingCount) . " columns. Please run the SQL commands first.</p>";
}

// Test 4: Function Test
echo "<h2>4. getPropertyById Function Test</h2>";
require_once 'includes/functions.php';

$testProperty = getPropertyById(1);
if ($testProperty) {
    echo "<p class='success'>✅ getPropertyById function works</p>";
    echo "<p>Testing property ID 1 features:</p>";
    echo "<ul>";
    echo "<li>⚡ Electricity: " . (isset($testProperty['has_electricity']) && $testProperty['has_electricity'] ? 'Yes' : 'No') . "</li>";
    echo "<li>💧 Water: " . (isset($testProperty['has_water']) && $testProperty['has_water'] ? 'Yes' : 'No') . "</li>";
    echo "<li>🔲 Fence: " . (isset($testProperty['has_fence']) && $testProperty['has_fence'] ? 'Yes' : 'No') . "</li>";
    echo "</ul>";
} else {
    echo "<p class='error'>❌ getPropertyById function failed or no properties exist</p>";
}

// Test 5: Summary
echo "<h2>5. Implementation Summary</h2>";

echo "<div style='background: #f9f9f9; padding: 15px; border-radius: 5px;'>";
echo "<h3>Database Status:</h3>";
if ($existingCount === $totalColumns) {
    echo "<p class='success'>✅ All feature columns exist in database</p>";
} else {
    echo "<p class='error'>❌ Missing " . ($totalColumns - $existingCount) . " columns</p>";
    echo "<p class='warning'>⚠️ Please run the SQL script: add_features_columns.sql</p>";
}

echo "<h3>Code Implementation Status:</h3>";
echo "<ul>";
echo "<li class='success'>✅ Form checkboxes added to add_property.php</li>";
echo "<li class='success'>✅ PHP variables and database insertion logic updated</li>";
echo "<li class='success'>✅ Property details page updated to display features</li>";
echo "<li class='success'>✅ getPropertyById function updated to fetch features</li>";
echo "<li class='success'>✅ Pay Now button present in property.php</li>";
echo "</ul>";

if ($existingCount === $totalColumns) {
    echo "<h2 style='color: green;'>🎉 Property Features System is FULLY IMPLEMENTED!</h2>";
    echo "<p><strong>You can now:</strong></p>";
    echo "<ul>";
    echo "<li>Add properties with features using the add property form</li>";
    echo "<li>View features on property details pages</li>";
    echo "<li>Use the Pay Now button for property purchases</li>";
    echo "</ul>";
} else {
    echo "<h2 style='color: orange;'>⚠️ Please add missing database columns first</h2>";
    echo "<p>Run the SQL commands in add_features_columns.sql</p>";
    echo "<p><strong>SQL Commands to run in phpMyAdmin:</strong></p>";
    echo "<div style='background: #fff; padding: 10px; border: 1px solid #ccc; font-family: monospace;'>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_electricity TINYINT(1) DEFAULT 0;<br>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_water TINYINT(1) DEFAULT 0;<br>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_fence TINYINT(1) DEFAULT 0;<br>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_gym TINYINT(1) DEFAULT 0;<br>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_air_condition TINYINT(1) DEFAULT 0;<br>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_security TINYINT(1) DEFAULT 0;<br>";
    echo "ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_pool TINYINT(1) DEFAULT 0;";
    echo "</div>";
}
echo "</div>";

echo "<h2>6. Quick Links</h2>";
echo "<p><a href='add_property.php' target='_blank'>➕ Add New Property</a></p>";
echo "<p><a href='index.php' target='_blank'>🏠 View Homepage</a></p>";
echo "<p><a href='seller_dashboard.php' target='_blank'>📊 Seller Dashboard</a></p>";

echo "</body></html>";
?> 