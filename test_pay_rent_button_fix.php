<?php
// Test Pay Rent Button Fix - Verify that duplicate "/month" text is fixed
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Pay Rent Button Fix</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔧 Pay Rent Button Fix Test</h1>";

// Step 1: Issue Fixed
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-check me-2'></i>Issue Fixed</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>❌ Before Fix:</h5>";
echo "<div class='alert alert-danger'>";
echo "<strong>Duplicate Text Issue:</strong><br>";
echo "• Pay Rent - TZS 23,000/month/month<br>";
echo "• Pay Rent - TZS 50,000/month/month<br>";
echo "• Caused by adding '/month' to already formatted price";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>✅ After Fix:</h5>";
echo "<div class='alert alert-success'>";
echo "<strong>Correct Display:</strong><br>";
echo "• Pay Rent - TZS 23,000/month<br>";
echo "• Pay Rent - TZS 50,000/month<br>";
echo "• Uses formatPrice() function output directly";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h6>Technical Details:</h6>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<strong>Root Cause:</strong>";
echo "<pre class='bg-light p-2 mt-2'>";
echo "formatPrice() function already returns:\n";
echo "\"TZS 23,000/month\" for rental properties\n\n";
echo "But property.php was adding '/month' again:\n";
echo "Pay Rent - <?php echo \$formattedPrice; ?>/month";
echo "</pre>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<strong>Fix Applied:</strong>";
echo "<pre class='bg-light p-2 mt-2'>";
echo "Removed duplicate '/month' from button text:\n\n";
echo "Before:\n";
echo "Pay Rent - <?php echo \$formattedPrice; ?>/month\n\n";
echo "After:\n";
echo "Pay Rent - <?php echo \$formattedPrice; ?>";
echo "</pre>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Step 2: Test formatPrice Function
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-test-tube me-2'></i>formatPrice() Function Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>Testing formatPrice() Function Output:</h5>";

$testPrices = [
    ['price' => 25000, 'type' => 'Rent'],
    ['price' => 50000, 'type' => 'Rent'],
    ['price' => 75000, 'type' => 'Rent'],
    ['price' => 100000, 'type' => 'Sale'],
    ['price' => 200000, 'type' => 'Sale']
];

echo "<div class='row'>";
foreach ($testPrices as $test) {
    $formatted = formatPrice($test['price'], $test['type']);
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>{$test['type']} Property</h6>";
    echo "<p class='card-text'>";
    echo "<strong>Input:</strong> TZS " . number_format($test['price']) . " ({$test['type']})<br>";
    echo "<strong>Output:</strong> <span class='text-success'>{$formatted}</span>";
    echo "</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h6><i class='fas fa-info-circle me-2'></i>Function Behavior:</h6>";
echo "<ul class='mb-0'>";
echo "<li><strong>Rental Properties:</strong> Automatically adds '/month' suffix</li>";
echo "<li><strong>Sale Properties:</strong> No suffix added</li>";
echo "<li><strong>Consistent Formatting:</strong> Always includes 'TZS' prefix</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

// Step 3: Available Rental Properties for Testing
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-home me-2'></i>Test Rental Properties</h3>";
echo "</div>";
echo "<div class='card-body'>";

$rentalProperties = $conn->query("SELECT id, title, price, location, listing_type FROM properties WHERE listing_type = 'Rent' AND status = 'Available' ORDER BY id LIMIT 5")->fetch_all(MYSQLI_ASSOC);

if (empty($rentalProperties)) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>No rental properties found for testing.";
    echo "</div>";
} else {
    echo "<h5>Available Rental Properties (Test the Fix):</h5>";
    echo "<div class='row'>";
    foreach ($rentalProperties as $prop) {
        $formattedPrice = formatPrice($prop['price'], $prop['listing_type']);
        echo "<div class='col-md-6 col-lg-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>Property #{$prop['id']}</h6>";
        echo "<p class='card-text'>" . htmlspecialchars(substr($prop['title'], 0, 30)) . "...</p>";
        echo "<p class='text-success'><strong>$formattedPrice</strong></p>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='property.php?id={$prop['id']}' class='btn btn-primary btn-sm' target='_blank'>Test Property Page</a>";
        echo "<div class='btn btn-success btn-sm disabled'>Pay Rent - $formattedPrice</div>";
        echo "</div>";
        echo "<small class='text-muted'>Button text should show correct format above</small>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div></div>";

// Step 4: Testing Instructions
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'><i class='fas fa-clipboard-list me-2'></i>Testing Instructions</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>🧪 How to Verify the Fix:</h5>";
echo "<ol>";
echo "<li><strong>Visit rental property pages</strong> from the list above</li>";
echo "<li><strong>Check Pay Rent button text</strong> - should show 'Pay Rent - TZS X,XXX/month'</li>";
echo "<li><strong>Verify no duplicates</strong> - should NOT show 'Pay Rent - TZS X,XXX/month/month'</li>";
echo "<li><strong>Test on both desktop and mobile</strong> - both versions should be fixed</li>";
echo "<li><strong>Compare with sale properties</strong> - should show 'Pay Now - TZS X,XXX' (no /month)</li>";
echo "</ol>";

echo "<div class='alert alert-success'>";
echo "<h6><i class='fas fa-check-circle me-2'></i>Expected Results:</h6>";
echo "<ul class='mb-0'>";
echo "<li>✅ Rental properties: 'Pay Rent - TZS 25,000/month'</li>";
echo "<li>✅ Sale properties: 'Pay Now - TZS 100,000'</li>";
echo "<li>✅ No duplicate '/month' text anywhere</li>";
echo "<li>✅ Consistent formatting across all pages</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

// Action Buttons
echo "<div class='text-center'>";
echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='index.php' class='btn btn-primary' target='_blank'><i class='fas fa-home me-2'></i>Test Homepage</a>";
echo "<a href='search.php?listing=Rent' class='btn btn-info' target='_blank'><i class='fas fa-search me-2'></i>Test Rental Search</a>";
if (!empty($rentalProperties)) {
    echo "<a href='property.php?id={$rentalProperties[0]['id']}' class='btn btn-success' target='_blank'><i class='fas fa-eye me-2'></i>Test Property Page</a>";
}
echo "</div>";
echo "</div>";

// Final Status
echo "<div class='text-center mt-4'>";
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>✅ PAY RENT BUTTON FIX COMPLETE!</h4>";
echo "<p class='mb-0'>Duplicate '/month' text has been removed from Pay Rent buttons. All rental property buttons now display correctly.</p>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
