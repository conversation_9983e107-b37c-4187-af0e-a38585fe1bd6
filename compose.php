<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php?redirect=compose.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Initialize variables
$receiver = '';
$subject = '';
$message = '';
$propertyId = '';
$error = '';
$success = '';

// Get property ID from query string if available
if (isset($_GET['property_id']) && is_numeric($_GET['property_id'])) {
    $propertyId = (int) $_GET['property_id'];
    
    // Get property details
    $sql = "SELECT p.*, u.username as seller_name, u.id as seller_id 
            FROM properties p
            JOIN users u ON p.seller_id = u.id
            WHERE p.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $propertyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $property = $result->fetch_assoc();
        $receiver = $property['seller_id'];
        $subject = 'Inquiry about: ' . $property['title'];
    }
}

// Get users for the receiver dropdown
$sql = "SELECT id, username, email FROM users WHERE id != ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$users = [];
while ($row = $result->fetch_assoc()) {
    $users[] = $row;
}

// Get properties for the property dropdown
$sql = "SELECT id, title FROM properties WHERE status = 'Available'";
$result = $conn->query($sql);
$properties = [];
while ($row = $result->fetch_assoc()) {
    $properties[] = $row;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $receiver = isset($_POST['receiver']) ? (int) $_POST['receiver'] : 0;
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    $propertyId = !empty($_POST['property_id']) ? (int) $_POST['property_id'] : NULL;
    
    // Validate inputs
    if (empty($receiver)) {
        $error = "Please select a recipient.";
    } elseif (empty($subject)) {
        $error = "Subject is required.";
    } elseif (empty($message)) {
        $error = "Message is required.";
    } else {
        // Insert message
        $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, read_status) 
                VALUES (?, ?, ?, ?, ?, 0)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiiss", $userId, $receiver, $propertyId, $subject, $message);
        
        if ($stmt->execute()) {
            $success = "Message sent successfully!";
            
            // Clear form
            $receiver = '';
            $subject = '';
            $message = '';
            $propertyId = '';
        } else {
            $error = "Error sending message. Please try again.";
        }
    }
}
?>

<!-- Compose Message Page -->
<div class="container my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">Compose Message</h1>
                <div>
                    <a href="inbox.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Inbox
                    </a>
                </div>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">New Message</h5>
                </div>
                <div class="card-body">
                    <form action="compose.php" method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="receiver" class="form-label">To <span class="text-danger">*</span></label>
                            <select class="form-select" id="receiver" name="receiver" required>
                                <option value="">Select Recipient</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $receiver == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo $user['username']; ?> (<?php echo $user['email']; ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a recipient.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="property_id" class="form-label">Related Property</label>
                            <select class="form-select" id="property_id" name="property_id">
                                <option value="">None</option>
                                <?php foreach ($properties as $property): ?>
                                    <option value="<?php echo $property['id']; ?>" <?php echo $propertyId == $property['id'] ? 'selected' : ''; ?>>
                                        <?php echo $property['title']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo $subject; ?>" required>
                            <div class="invalid-feedback">
                                Please provide a subject.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="10" required><?php echo $message; ?></textarea>
                            <div class="invalid-feedback">
                                Please provide a message.
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary me-md-2">Clear</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i> Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?> 