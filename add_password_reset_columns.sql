-- Add password reset columns to users table
-- Run this SQL to add password reset functionality

ALTER TABLE users 
ADD COLUMN reset_token VARCHAR(64) NULL,
ADD COLUMN reset_token_expires DATETIME NULL;

-- Add index for faster token lookups
CREATE INDEX idx_reset_token ON users(reset_token);
CREATE INDEX idx_reset_token_expires ON users(reset_token_expires);

-- Optional: Clean up any existing expired tokens
UPDATE users 
SET reset_token = NULL, reset_token_expires = NULL 
WHERE reset_token_expires < NOW();
