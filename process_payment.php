<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/email_helper.php';

// Flutterwave verification block
if (isset($_GET['transaction_id'])) {
    $transactionId = $_GET['transaction_id'];
    $propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;
    $amount = isset($_GET['amount']) ? floatval($_GET['amount']) : 0;
    $sellerId = isset($_GET['seller_id']) ? (int)$_GET['seller_id'] : 0;
    $sellerName = isset($_GET['seller_name']) ? $_GET['seller_name'] : '';

    // Verify transaction with Flutterwave
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/$transactionId/verify",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => array(
            "Authorization: Bearer FLWSECK_TESTf46c075d9948"
        ),
    ));
    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);

    if ($err) {
        die("cURL Error #: $err");
    } else {
        $result = json_decode($response, true);
        if ($result['status'] == 'success' && $result['data']['status'] == 'successful') {
            // Set POST variables for the rest of the script
            $_POST['property_id'] = $propertyId;
            $_POST['amount'] = $amount;
            $_POST['seller_id'] = $sellerId;
            $_POST['seller_name'] = $sellerName;
        } else {
            die('Payment verification failed.');
        }
    }
}

if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    die('Not authorized.');
}

$buyerId = $_SESSION['user_id'];
$buyerName = $_SESSION['username'];
$buyerEmail = $_SESSION['email'];

$propertyId = isset($_POST['property_id']) ? (int)$_POST['property_id'] : 0;
$amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
$sellerId = isset($_POST['seller_id']) ? (int)$_POST['seller_id'] : 0;
$sellerName = isset($_POST['seller_name']) ? $_POST['seller_name'] : '';

if (!$propertyId || !$amount || !$sellerId) {
    die('Invalid payment data.');
}

// Simulate payment processing (always success)
$transactionId = 'TXN' . time() . rand(1000, 9999);
$now = date('Y-m-d H:i:s');

// Insert payment record
$sql = "INSERT INTO payments (property_id, buyer_id, seller_id, amount, date, status, transaction_id, payment_method) VALUES (?, ?, ?, ?, ?, 'completed', ?, 'Credit Card')";
$stmt = $conn->prepare($sql);
$stmt->bind_param('iiidss', $propertyId, $buyerId, $sellerId, $amount, $now, $transactionId);
$stmt->execute();

// Mark property as sold
$sql = "UPDATE properties SET status = 'Sold' WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $propertyId);
$stmt->execute();

// Get property title for messages/email
$sql = "SELECT title FROM properties WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $propertyId);
$stmt->execute();
$result = $stmt->get_result();
$propertyTitle = $result->fetch_assoc()['title'] ?? '';

// Send internal messages
// To Buyer
$buyerMsg = "[System] You made a payment of TZS " . number_format($amount, 2) . " for Property ID $propertyId to seller $sellerName.";
$buyerSubject = "[System] Payment Confirmation - Property ID: $propertyId";
$sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
$stmt = $conn->prepare($sql);
$stmt->bind_param('iiiss', $sellerId, $buyerId, $propertyId, $buyerSubject, $buyerMsg);
$stmt->execute();
// To Seller
$sellerMsg = "[System] You received a payment of TZS " . number_format($amount, 2) . " from buyer $buyerName for Property ID $propertyId.";
$sellerSubject = "[System] Payment Received - Property ID: $propertyId";
$sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
$stmt = $conn->prepare($sql);
$stmt->bind_param('iiiss', $buyerId, $sellerId, $propertyId, $sellerSubject, $sellerMsg);
$stmt->execute();

// Send email to buyer
$emailData = [
    'name' => $buyerName,
    'email' => $buyerEmail,
    'transaction_id' => $transactionId,
    'amount' => $amount,
    'property_title' => $propertyTitle,
    'property_id' => $propertyId
];
sendPaymentConfirmationEmail($emailData);

// Output JS to close tab and redirect opener
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Payment Successful</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container py-5">
        <div class="alert alert-success text-center">
            <h2>Payment Successful!</h2>
            <p>Your payment has been processed. This window will close automatically.</p>
        </div>
    </div>
    <script>
        setTimeout(function() {
            if (window.opener) {
                window.opener.location.href = 'my_paid_properties.php?success=1';
            }
            window.close();
        }, 2000);
    </script>
</body>
</html> 