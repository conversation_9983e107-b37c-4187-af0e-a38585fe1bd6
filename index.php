<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Get latest properties
$latestProperties = getLatestProperties(6);

// Define Tanzania regions
$regions = [
    'Arusha',
    'Dar es Salaam',
    'Dodoma',
    'Mbeya',
    'Tanga',
    'Zanzibar'
];

// Get property locations for the dropdown
$locations = getPropertyLocations();
?>

<!-- Hero Section with Search -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h1 class="fade-in">Find Your Dream Property in Tanzania</h1>
                <p class="fade-in fade-in-delay-1">
                    Browse our extensive collection of houses, apartments, and land 
                    available for sale and rent across Tanzania.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Property Search Form -->
<div class="container">
    <div class="search-form mb-5">
        <form action="search.php" method="GET" onsubmit="filterProperties(event)">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="location" class="form-label">Location</label>
                    <select class="form-select" id="location" name="location">
                        <option value="">All Locations</option>
                        <?php foreach ($regions as $region): ?>
                            <option value="<?php echo $region; ?>"><?php echo $region; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="property-type" class="form-label">Property Type</label>
                    <select class="form-select" id="property-type" name="type">
                        <option value="">All Types</option>
                        <option value="Land">Land</option>
                        <option value="House">House</option>
                        <option value="Apartment">Apartment</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="listing-type" class="form-label">For</label>
                    <select class="form-select" id="listing-type" name="listing">
                        <option value="">Sale & Rent</option>
                        <option value="Sale">Sale</option>
                        <option value="Rent">Rent</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-search w-100">
                        <i class="fas fa-search me-1"></i> Search
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Latest Properties Section -->
<section class="latest-properties py-5">
    <div class="container">
        <h2 class="section-title">Latest Properties</h2>
        
        <div class="row">
            <?php if (empty($latestProperties)): ?>
                <div class="col-12">
                    <div class="alert alert-info">No properties found. Check back soon!</div>
                </div>
            <?php else: ?>
                <?php foreach ($latestProperties as $property): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card property-card h-100">
                            <div class="position-relative">
                                <img src="<?php echo $property['primary_image']; ?>" class="card-img-top property-img" alt="<?php echo $property['title']; ?>">
                                <span class="badge position-absolute top-0 start-0 m-2 bg-<?php echo $property['status'] == 'Available' ? 'success' : ($property['status'] == 'Sold' ? 'danger' : 'secondary'); ?>"> <?php echo $property['status']; ?> </span>
                                <span class="badge position-absolute top-0 end-0 m-2 <?php echo $property['listing_type'] == 'Sale' ? 'badge-sale' : 'badge-rent'; ?>"> <?php echo $property['listing_type']; ?> </span>
                                <?php if ($property['featured']): ?>
                                    <span class="badge bg-warning position-absolute top-0 start-50 translate-middle-x mt-2">Featured</span>
                                <?php endif; ?>
                                <a href="#" class="btn btn-sm btn-light position-absolute top-0 end-0 m-2" onclick="toggleFavorite(event, <?php echo $property['id']; ?>)">
                                    <i class="far fa-heart"></i>
                                </a>
                            </div>
                            <div class="card-body">
                                <h5 class="property-title">
                                    <a href="property.php?id=<?php echo $property['id']; ?>" class="text-decoration-none">
                                        <?php echo $property['title']; ?>
                                    </a>
                                </h5>
                                <p class="property-price"><?php echo formatPrice($property['price'], $property['listing_type']); ?></p>
                                <p class="property-location"><i class="fas fa-map-marker-alt"></i> 
                                    <?php 
                                    if (isset($property['location_display'])) {
                                        echo $property['location_display'];
                                    } else {
                                        echo $property['location'];
                                    }
                                    ?>
                                </p>
                                <p class="card-text"><?php echo substr($property['description'], 0, 100); ?>...</p>
                            </div>
                            <div class="card-footer bg-white">
                                <div class="property-features">
                                    <?php if ($property['bedrooms']): ?>
                                        <span class="property-feature"><i class="fas fa-bed"></i> <?php echo $property['bedrooms']; ?> Beds</span>
                                    <?php endif; ?>
                                    <?php if ($property['bathrooms']): ?>
                                        <span class="property-feature"><i class="fas fa-bath"></i> <?php echo $property['bathrooms']; ?> Baths</span>
                                    <?php endif; ?>
                                    <?php if ($property['area']): ?>
                                        <span class="property-feature"><i class="fas fa-vector-square"></i> <?php echo $property['area']; ?> m²</span>
                                    <?php endif; ?>
                                    
                                    <?php if (isset($property['has_electricity']) && $property['has_electricity']): ?>
                                        <span class="property-feature"><i class="fas fa-bolt text-warning"></i></span>
                                    <?php endif; ?>
                                    <?php if (isset($property['has_security']) && $property['has_security']): ?>
                                        <span class="property-feature"><i class="fas fa-shield-alt text-success"></i></span>
                                    <?php endif; ?>
                                    <?php if (isset($property['has_air_condition']) && $property['has_air_condition']): ?>
                                        <span class="property-feature"><i class="fas fa-snowflake text-info"></i></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="search.php" class="btn btn-primary">View All Properties</a>
        </div>
    </div>
</section>

<!-- Featured Locations Section -->
<section class="featured-locations py-5 bg-light">
    <div class="container">
        <h2 class="section-title">Featured Regions</h2>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <img src="assets/images/dar-es-salaam.jpg" class="card-img-top" alt="Dar es Salaam" style="height: 200px; object-fit: cover;">
                    <div class="card-body text-center">
                        <h5 class="card-title">Dar es Salaam</h5>
                        <a href="search.php?location=Dar es Salaam" class="btn btn-outline-secondary">View Properties</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <img src="assets/images/arusha.jpg" class="card-img-top" alt="Arusha" style="height: 200px; object-fit: cover;">
                    <div class="card-body text-center">
                        <h5 class="card-title">Arusha</h5>
                        <a href="search.php?location=Arusha" class="btn btn-outline-secondary">View Properties</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <img src="assets/images/dodoma.jpg" class="card-img-top" alt="Dodoma" style="height: 200px; object-fit: cover;">
                    <div class="card-body text-center">
                        <h5 class="card-title">Dodoma</h5>
                        <a href="search.php?location=Dodoma" class="btn btn-outline-secondary">View Properties</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="why-choose-us py-5">
    <div class="container">
        <h2 class="section-title">Why Choose TX PROPERTIES</h2>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-home fa-3x mb-3 text-primary"></i>
                    <h4>Wide Range of Properties</h4>
                    <p>Browse through our extensive collection of properties across Tanzania, from luxurious villas to affordable apartments.</p>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-shield-alt fa-3x mb-3 text-primary"></i>
                    <h4>Trusted Platform</h4>
                    <p>Join thousands of satisfied customers who have found their dream properties through our trusted platform.</p>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="text-center">
                    <i class="fas fa-headset fa-3x mb-3 text-primary"></i>
                    <h4>24/7 Support</h4>
                    <p>Our dedicated team is always available to assist you with any queries or concerns throughout your property journey.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
require_once 'includes/footer.php';
?> 