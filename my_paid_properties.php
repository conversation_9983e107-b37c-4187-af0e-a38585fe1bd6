<?php
require_once 'includes/header.php';
require_once 'includes/functions.php';

if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    redirect('login.php');
}

$buyerId = $_SESSION['user_id'];

// Get all paid properties for this buyer
$sql = "SELECT p.*, pay.amount, pay.date FROM payments pay JOIN properties p ON pay.property_id = p.id WHERE pay.buyer_id = ? AND pay.status = 'completed' ORDER BY pay.date DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $buyerId);
$stmt->execute();
$result = $stmt->get_result();
$properties = [];
while ($row = $result->fetch_assoc()) {
    $properties[] = $row;
}
?>
<div class="container my-4">
    <h1 class="mb-4">My Paid Properties</h1>
    <?php if (isset($_GET['success'])): ?>
        <div class="alert alert-success">Payment Successful! Your property is now listed here.</div>
    <?php endif; ?>
    <?php if (empty($properties)): ?>
        <div class="alert alert-info">You have not paid for any properties yet.</div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($properties as $property): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <img src="<?php echo htmlspecialchars(getPrimaryImage($property['id'])); ?>" class="card-img-top" alt="Property Image">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($property['title']); ?></h5>
                            <p class="card-text text-muted">
                                <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($property['location']); ?>
                            </p>
                            <p class="card-text fw-bold mb-2">TZS <?php echo number_format($property['amount']); ?></p>
                            <a href="property.php?id=<?php echo $property['id']; ?>" class="btn btn-primary btn-sm mb-2">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </div>
                        <div class="card-footer text-muted small">
                            Paid on <?php echo date('M d, Y', strtotime($property['date'])); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>
<?php require_once 'includes/footer.php'; ?> 