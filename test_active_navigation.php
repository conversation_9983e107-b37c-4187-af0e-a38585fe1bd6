<?php
// Test Active Navigation - Verify that active page highlighting is working
require_once 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h1 class="mb-0"><i class="fas fa-navigation me-2"></i>Active Navigation Test</h1>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>Testing Active Navigation Highlighting</h5>
                        <p class="mb-0">This page tests the active navigation highlighting system. The current page should be visually highlighted in the navigation bar.</p>
                    </div>

                    <h3>🔍 Current Page Detection</h3>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Current Page Info:</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>File Name:</strong> <?php echo getCurrentPage(); ?></li>
                                        <li><strong>Full Path:</strong> <?php echo $_SERVER['PHP_SELF']; ?></li>
                                        <li><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Active Page Tests:</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Is Index:</strong> <?php echo isActivePage('index.php') ? '✅ Yes' : '❌ No'; ?></li>
                                        <li><strong>Is About:</strong> <?php echo isActivePage('about.php') ? '✅ Yes' : '❌ No'; ?></li>
                                        <li><strong>Is Contact:</strong> <?php echo isActivePage('contact.php') ? '✅ Yes' : '❌ No'; ?></li>
                                        <li><strong>Is Search:</strong> <?php echo isActivePage('search_location.php') ? '✅ Yes' : '❌ No'; ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3>🎯 Navigation Test Links</h3>
                    <p>Click these links to test active navigation highlighting on different pages:</p>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h5>Main Navigation:</h5>
                            <div class="d-grid gap-2">
                                <a href="index.php" class="btn btn-outline-primary">
                                    <i class="fas fa-home me-2"></i>Home Page
                                </a>
                                <a href="search_location.php" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-2"></i>Search by Location
                                </a>
                                <a href="about.php" class="btn btn-outline-primary">
                                    <i class="fas fa-info-circle me-2"></i>About Us
                                </a>
                                <a href="contact.php" class="btn btn-outline-primary">
                                    <i class="fas fa-envelope me-2"></i>Contact Us
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <h5>User Pages:</h5>
                            <div class="d-grid gap-2">
                                <?php if (!isLoggedIn()): ?>
                                    <a href="login.php" class="btn btn-outline-success">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login Page
                                    </a>
                                    <a href="register.php" class="btn btn-outline-success">
                                        <i class="fas fa-user-plus me-2"></i>Register Page
                                    </a>
                                <?php else: ?>
                                    <a href="profile.php" class="btn btn-outline-success">
                                        <i class="fas fa-user me-2"></i>My Profile
                                    </a>
                                    <?php if (isSeller()): ?>
                                        <a href="seller_dashboard.php" class="btn btn-outline-success">
                                            <i class="fas fa-dashboard me-2"></i>Seller Dashboard
                                        </a>
                                        <a href="add_property.php" class="btn btn-outline-success">
                                            <i class="fas fa-plus me-2"></i>Add Property
                                        </a>
                                        <a href="my_properties.php" class="btn btn-outline-success">
                                            <i class="fas fa-home me-2"></i>My Properties
                                        </a>
                                        <a href="inbox.php" class="btn btn-outline-success">
                                            <i class="fas fa-envelope me-2"></i>Seller Inbox
                                        </a>
                                    <?php else: ?>
                                        <a href="buyer_inbox.php" class="btn btn-outline-success">
                                            <i class="fas fa-envelope me-2"></i>Buyer Inbox
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <h3>✅ Expected Behavior</h3>
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>What to Look For:</h6>
                        <ul class="mb-0">
                            <li><strong>Desktop:</strong> Active page should have a different background color, bold text, and bottom border</li>
                            <li><strong>Mobile:</strong> Active page should have a left border and indented padding</li>
                            <li><strong>Dropdown Items:</strong> Active dropdown items should have a dark background</li>
                            <li><strong>Persistent:</strong> Active styling should remain even when not hovering</li>
                            <li><strong>Related Pages:</strong> Some pages activate parent menu items (e.g., search.php activates "Search by Location")</li>
                        </ul>
                    </div>

                    <h3>🎨 Active Styling Features</h3>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-desktop fa-2x text-primary mb-2"></i>
                                    <h6>Desktop Styling</h6>
                                    <small class="text-muted">Background color, bold text, bottom border</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                    <h6>Mobile Styling</h6>
                                    <small class="text-muted">Left border, indented padding</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-list fa-2x text-warning mb-2"></i>
                                    <h6>Dropdown Items</h6>
                                    <small class="text-muted">Dark background for active items</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="index.php" class="btn btn-primary me-2">
                            <i class="fas fa-home me-2"></i>Back to Homepage
                        </a>
                        <button onclick="window.location.reload()" class="btn btn-secondary">
                            <i class="fas fa-refresh me-2"></i>Refresh Test
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Additional test page styling */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}
</style>

<?php require_once 'includes/footer.php'; ?>
