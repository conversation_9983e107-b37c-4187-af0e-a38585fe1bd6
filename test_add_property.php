<?php
// Test Add Property Database Connection
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Add Property Test</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🧪 Add Property Database Test</h1>";

// Test database connection
if ($conn->connect_error) {
    echo "<p class='error'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit;
}

echo "<p class='success'>✅ Database connection successful</p>";

// Check if properties table exists
$result = $conn->query("SHOW TABLES LIKE 'properties'");
if ($result->num_rows == 0) {
    echo "<p class='error'>❌ Properties table does not exist</p>";
    exit;
}

echo "<p class='success'>✅ Properties table exists</p>";

// Check current table structure
echo "<h2>📊 Current Table Structure</h2>";
$result = $conn->query("DESCRIBE properties");
echo "<table border='1' style='border-collapse:collapse; margin:20px 0;'>";
echo "<tr style='background:#f0f0f0;'><th style='padding:10px;'>Column</th><th style='padding:10px;'>Type</th><th style='padding:10px;'>Null</th><th style='padding:10px;'>Key</th><th style='padding:10px;'>Default</th></tr>";

$hasLocalArea = false;
$hasFeatures = false;

while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td style='padding:8px;'>" . $row['Field'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Type'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Null'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Key'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Default'] . "</td>";
    echo "</tr>";
    
    if ($row['Field'] == 'local_area') $hasLocalArea = true;
    if ($row['Field'] == 'has_electricity') $hasFeatures = true;
}
echo "</table>";

// Test column detection logic
echo "<h2>🔍 Column Detection Test</h2>";
$checkColumns = $conn->query("SHOW COLUMNS FROM properties LIKE 'local_area'");
$hasNewColumns = $checkColumns->num_rows > 0;

if ($hasNewColumns) {
    echo "<p class='success'>✅ New columns detected - Full feature mode available</p>";
    echo "<p class='info'>ℹ️ The add property form will include:</p>";
    echo "<ul>";
    echo "<li>Local area field</li>";
    echo "<li>Property features (electricity, gym, AC, security, pool)</li>";
    echo "<li>Enhanced property types</li>";
    echo "</ul>";
} else {
    echo "<p class='info'>ℹ️ Basic columns only - Compatibility mode</p>";
    echo "<p class='info'>ℹ️ The add property form will include basic fields only</p>";
    echo "<p>To enable full features, run: <a href='fix_database.php' style='color:blue;'>fix_database.php</a></p>";
}

// Test SQL query generation
echo "<h2>🔧 SQL Query Test</h2>";

if ($hasNewColumns) {
    $sql = "INSERT INTO properties (title, description, price, location, local_area, property_type, listing_type, 
            bedrooms, bathrooms, area, seller_id, lat, lng, has_electricity, has_gym, has_air_condition, has_security, has_pool) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $paramCount = 18;
    $typeString = "ssdssssiiidddiiiii";
} else {
    $sql = "INSERT INTO properties (title, description, price, location, property_type, listing_type, 
            bedrooms, bathrooms, area, seller_id, lat, lng) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $paramCount = 12;
    $typeString = "ssdsssiiiddd";
}

echo "<p><strong>SQL Query:</strong></p>";
echo "<pre style='background:#f5f5f5; padding:15px; border-radius:5px; overflow-x:auto;'>" . htmlspecialchars($sql) . "</pre>";

echo "<p><strong>Parameter Count:</strong> $paramCount</p>";
echo "<p><strong>Type String:</strong> <code>$typeString</code> (Length: " . strlen($typeString) . ")</p>";

if (strlen($typeString) == $paramCount) {
    echo "<p class='success'>✅ Parameter count matches type string length</p>";
} else {
    echo "<p class='error'>❌ Parameter count mismatch!</p>";
}

// Test prepared statement
echo "<h2>🧪 Prepared Statement Test</h2>";
try {
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        echo "<p class='success'>✅ Prepared statement created successfully</p>";
        $stmt->close();
    } else {
        echo "<p class='error'>❌ Failed to prepare statement: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Exception: " . $e->getMessage() . "</p>";
}

echo "<div style='background:#f0f8ff; padding:20px; border-radius:10px; margin:30px 0;'>";
echo "<h2>🎯 Summary</h2>";
if ($hasNewColumns) {
    echo "<p class='success'><strong>✅ Your database is fully updated!</strong></p>";
    echo "<p>You can use all features of the Add Property form.</p>";
} else {
    echo "<p class='info'><strong>ℹ️ Your database is using basic structure</strong></p>";
    echo "<p>The Add Property form will work in compatibility mode.</p>";
    echo "<p><strong>To enable full features:</strong> <a href='fix_database.php' style='background:#007bff; color:white; padding:8px 16px; text-decoration:none; border-radius:5px;'>Run Database Update</a></p>";
}
echo "</div>";

echo "<div style='text-align:center; margin:30px 0;'>";
echo "<a href='add_property.php' style='background:#28a745; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Test Add Property</a>";
echo "<a href='index.php' style='background:#007bff; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Go to Homepage</a>";
echo "</div>";

$conn->close();
echo "</body></html>";
?>
