<?php
// Check and Add Property Features Script
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Property Features Implementation</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}</style>";
echo "</head><body>";

echo "<h1>🏠 Property Features Implementation</h1>";
echo "<p>This script will check and add the missing property feature columns to your database.</p>";

// Step 1: Check current table structure
echo "<h2>📊 Step 1: Checking Current Table Structure</h2>";
$result = $conn->query("DESCRIBE properties");
echo "<table border='1' style='border-collapse:collapse; margin:20px 0;'>";
echo "<tr style='background:#f0f0f0;'><th style='padding:10px;'>Column</th><th style='padding:10px;'>Type</th><th style='padding:10px;'>Null</th><th style='padding:10px;'>Default</th></tr>";

$existingColumns = [];
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td style='padding:8px;'>" . $row['Field'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Type'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Null'] . "</td>";
    echo "<td style='padding:8px;'>" . $row['Default'] . "</td>";
    echo "</tr>";
    $existingColumns[] = $row['Field'];
}
echo "</table>";

// Step 2: Check which feature columns are missing
echo "<h2>🔍 Step 2: Checking Feature Columns</h2>";
$requiredFeatures = [
    'has_electricity' => 'Electricity availability',
    'has_water' => 'Water supply availability',
    'has_fence' => 'Property fencing',
    'has_gym' => 'Gym facilities',
    'has_air_condition' => 'Air conditioning',
    'has_security' => 'Security features',
    'has_pool' => 'Swimming pool'
];

$missingColumns = [];
foreach ($requiredFeatures as $column => $description) {
    if (in_array($column, $existingColumns)) {
        echo "<p class='success'>✅ $column ($description) - EXISTS</p>";
    } else {
        echo "<p class='warning'>⚠️ $column ($description) - MISSING</p>";
        $missingColumns[] = $column;
    }
}

// Step 3: Add missing columns
if (!empty($missingColumns)) {
    echo "<h2>🔧 Step 3: Adding Missing Columns</h2>";
    
    foreach ($missingColumns as $column) {
        $sql = "ALTER TABLE properties ADD COLUMN $column TINYINT(1) DEFAULT 0";
        echo "<p><strong>Executing:</strong> <code>$sql</code></p>";
        
        try {
            if ($conn->query($sql)) {
                echo "<p class='success'>✅ Successfully added $column column</p>";
            } else {
                echo "<p class='error'>❌ Error adding $column: " . $conn->error . "</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Exception adding $column: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<h2>✅ Step 3: All Feature Columns Already Exist</h2>";
    echo "<p class='success'>No columns need to be added.</p>";
}

// Step 4: Verify final structure
echo "<h2>🎯 Step 4: Final Verification</h2>";
$result = $conn->query("DESCRIBE properties");
$finalColumns = [];
while ($row = $result->fetch_assoc()) {
    $finalColumns[] = $row['Field'];
}

foreach ($requiredFeatures as $column => $description) {
    if (in_array($column, $finalColumns)) {
        echo "<p class='success'>✅ $column - CONFIRMED IN DATABASE</p>";
    } else {
        echo "<p class='error'>❌ $column - STILL MISSING</p>";
    }
}

echo "<div style='background:#f0f8ff; padding:20px; border-radius:10px; margin:30px 0;'>";
echo "<h2>📋 Summary</h2>";
if (empty($missingColumns)) {
    echo "<p class='success'><strong>✅ All property feature columns are present in the database!</strong></p>";
} else {
    $addedCount = 0;
    foreach ($missingColumns as $column) {
        if (in_array($column, $finalColumns)) {
            $addedCount++;
        }
    }
    echo "<p><strong>Added $addedCount out of " . count($missingColumns) . " missing columns.</strong></p>";
}
echo "</div>";

echo "<div style='text-align:center; margin:30px 0;'>";
echo "<a href='add_property.php' style='background:#28a745; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Test Add Property Form</a>";
echo "<a href='property.php?id=1' style='background:#007bff; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Test Property Display</a>";
echo "<a href='index.php' style='background:#6c757d; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; margin:10px;'>Go to Homepage</a>";
echo "</div>";

$conn->close();
echo "</body></html>";
?>
