<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Handle mark as read action
if (isset($_GET['action']) && $_GET['action'] == 'mark_read' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $sql = "UPDATE contacts SET status = 'read' WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    redirect('contact_messages.php?success=1');
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $sql = "DELETE FROM contacts WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    redirect('contact_messages.php?deleted=1');
}

// Get messages from database
$sql = "SELECT * FROM contacts ORDER BY created_at DESC";
$result = $conn->query($sql);
$messages = [];

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $messages[] = $row;
    }
}

// Include admin header
include_once 'admin_header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contact Messages</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($_GET['success'])): ?>
                        <div class="alert alert-success">Message marked as read.</div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['deleted'])): ?>
                        <div class="alert alert-warning">Message deleted successfully.</div>
                    <?php endif; ?>
                    
                    <?php if (empty($messages)): ?>
                        <div class="alert alert-info">No contact messages found.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($messages as $message): ?>
                                        <tr class="<?php echo $message['status'] == 'unread' ? 'table-primary' : ''; ?>">
                                            <td><?php echo $message['id']; ?></td>
                                            <td><?php echo htmlspecialchars($message['name']); ?></td>
                                            <td><a href="mailto:<?php echo htmlspecialchars($message['email']); ?>"><?php echo htmlspecialchars($message['email']); ?></a></td>
                                            <td><?php echo htmlspecialchars($message['subject']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $message['status'] == 'unread' ? 'bg-primary' : 'bg-secondary'; ?>">
                                                    <?php echo ucfirst($message['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y H:i', strtotime($message['created_at'])); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal<?php echo $message['id']; ?>">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                                
                                                <?php if ($message['status'] == 'unread'): ?>
                                                    <a href="contact_messages.php?action=mark_read&id=<?php echo $message['id']; ?>" class="btn btn-sm btn-success">
                                                        <i class="fas fa-check"></i> Mark Read
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <a href="contact_messages.php?action=delete&id=<?php echo $message['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this message?');">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                                
                                                <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=<?php echo rawurlencode('Re: ' . $message['subject']); ?>&body=<?php echo rawurlencode("\n\n--- Original Message ---\n" . $message['message']); ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-reply"></i> Reply
                                                </a>
                                            </td>
                                        </tr>
                                        
                                        <!-- Modal for viewing message -->
                                        <div class="modal fade" id="viewModal<?php echo $message['id']; ?>" tabindex="-1" aria-labelledby="viewModalLabel<?php echo $message['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="viewModalLabel<?php echo $message['id']; ?>"><?php echo htmlspecialchars($message['subject']); ?></h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong>From:</strong> <?php echo htmlspecialchars($message['name']); ?> (<?php echo htmlspecialchars($message['email']); ?>)</p>
                                                            </div>
                                                            <div class="col-md-6 text-md-end">
                                                                <p><strong>Date:</strong> <?php echo date('M d, Y H:i', strtotime($message['created_at'])); ?></p>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <div class="message-content">
                                                            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=<?php echo rawurlencode('Re: ' . $message['subject']); ?>&body=<?php echo rawurlencode("\n\n--- Original Message ---\n" . $message['message']); ?>" class="btn btn-primary">
                                                            <i class="fas fa-reply"></i> Reply via Email
                                                        </a>
                                                        <?php if ($message['status'] == 'unread'): ?>
                                                            <a href="contact_messages.php?action=mark_read&id=<?php echo $message['id']; ?>" class="btn btn-success">
                                                                <i class="fas fa-check"></i> Mark as Read
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include admin footer
include_once 'admin_footer.php';
?> 