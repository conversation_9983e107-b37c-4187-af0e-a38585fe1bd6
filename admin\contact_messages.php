<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Check if contact_messages table exists
$table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
if ($table_check->num_rows == 0) {
    // Create the table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        user_id INT NULL,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($create_table_sql) === TRUE) {
        echo "<script>alert('Contact messages table created successfully!');</script>";
    } else {
        echo "<script>alert('Error creating contact messages table: " . $conn->error . "');</script>";
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $sql = "DELETE FROM contact_messages WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    redirect('contact_messages.php?deleted=1');
}

// Get messages from database
$sql = "SELECT cm.*, u.full_name as user_full_name, u.email as user_email 
        FROM contact_messages cm 
        LEFT JOIN users u ON cm.user_id = u.id 
        ORDER BY cm.submitted_at DESC";
$result = $conn->query($sql);
$messages = [];

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $messages[] = $row;
    }
}

// Include admin header
include_once 'admin_header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contact Messages</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($_GET['deleted'])): ?>
                        <div class="alert alert-warning">Message deleted successfully.</div>
                    <?php endif; ?>
                    
                    <?php if (empty($messages)): ?>
                        <div class="alert alert-info">No contact messages found.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Subject</th>
                                        <th>User</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($messages as $message): ?>
                                        <tr>
                                            <td><?php echo $message['id']; ?></td>
                                            <td><?php echo htmlspecialchars($message['name']); ?></td>
                                            <td><a href="mailto:<?php echo htmlspecialchars($message['email']); ?>"><?php echo htmlspecialchars($message['email']); ?></a></td>
                                            <td><?php echo htmlspecialchars($message['subject']); ?></td>
                                            <td>
                                                <?php if ($message['user_id']): ?>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($message['user_full_name'] ?: 'Registered User'); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Guest</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('M d, Y H:i', strtotime($message['submitted_at'])); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal<?php echo $message['id']; ?>">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                                
                                                <a href="contact_messages.php?action=delete&id=<?php echo $message['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this message?');">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                                
                                                <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=<?php echo rawurlencode('Re: ' . $message['subject']); ?>&body=<?php echo rawurlencode("\n\n--- Original Message ---\n" . $message['message']); ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-reply"></i> Reply
                                                </a>
                                            </td>
                                        </tr>
                                        
                                        <!-- Modal for viewing message -->
                                        <div class="modal fade" id="viewModal<?php echo $message['id']; ?>" tabindex="-1" aria-labelledby="viewModalLabel<?php echo $message['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="viewModalLabel<?php echo $message['id']; ?>"><?php echo htmlspecialchars($message['subject']); ?></h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong>From:</strong> <?php echo htmlspecialchars($message['name']); ?> (<?php echo htmlspecialchars($message['email']); ?>)</p>
                                                                <?php if ($message['user_id']): ?>
                                                                    <p><strong>User:</strong> <?php echo htmlspecialchars($message['user_full_name'] ?: 'Registered User'); ?></p>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="col-md-6 text-md-end">
                                                                <p><strong>Date:</strong> <?php echo date('M d, Y H:i', strtotime($message['submitted_at'])); ?></p>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <div class="message-content">
                                                            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=<?php echo rawurlencode('Re: ' . $message['subject']); ?>&body=<?php echo rawurlencode("\n\n--- Original Message ---\n" . $message['message']); ?>" class="btn btn-primary">
                                                            <i class="fas fa-reply"></i> Reply via Email
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include admin footer
include_once 'admin_footer.php';
?> 