<?php
// Test Payment Fixes - Comprehensive Payment Flow Testing
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Payment Fixes</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔧 Payment Integration Fixes Test</h1>";

// Step 1: Issues Fixed
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-check me-2'></i>Issues Fixed</h3>";
echo "</div>";
echo "<div class='card-body'>";

$fixes = [
    'Enhanced Flutterwave Verification' => 'Improved API response handling with multiple success status checks',
    'Better Error Handling' => 'Detailed error messages and logging for debugging',
    'Callback Improvements' => 'Enhanced JavaScript callback with loading states',
    'Test Payment System' => 'Alternative payment processor for testing without API calls',
    'Property Status Updates' => 'Automatic property marking as sold after successful payment',
    'Listing Filtering' => 'Sold properties automatically hidden from public listings',
    'Success Page Redirect' => 'Proper redirect to detailed success page after payment'
];

echo "<div class='row'>";
foreach ($fixes as $fix => $description) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card h-100'>";
    echo "<div class='card-body'>";
    echo "<h6 class='card-title text-success'><i class='fas fa-wrench me-2'></i>$fix</h6>";
    echo "<p class='card-text text-muted'>$description</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "</div></div>";

// Step 2: Test Available Properties
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-home me-2'></i>Available Properties for Testing</h3>";
echo "</div>";
echo "<div class='card-body'>";

$availableProperties = $conn->query("SELECT id, title, status, price FROM properties WHERE status = 'Available' ORDER BY id LIMIT 5")->fetch_all(MYSQLI_ASSOC);

if (empty($availableProperties)) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>No available properties found. Please add some properties first.";
    echo "</div>";
} else {
    echo "<div class='row'>";
    foreach ($availableProperties as $prop) {
        echo "<div class='col-md-6 col-lg-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>Property #{$prop['id']}</h6>";
        echo "<p class='card-text'>" . htmlspecialchars(substr($prop['title'], 0, 40)) . "...</p>";
        echo "<p class='text-success'><strong>TZS " . number_format($prop['price']) . "</strong></p>";
        echo "<span class='badge bg-success mb-2'>{$prop['status']}</span>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='property.php?id={$prop['id']}' class='btn btn-primary btn-sm' target='_blank'>View Property</a>";
        echo "<a href='test_payment_processor.php?property_id={$prop['id']}' class='btn btn-warning btn-sm' target='_blank'>Test Payment</a>";
        echo "<a href='pay.php?property_id={$prop['id']}' class='btn btn-success btn-sm' target='_blank'>Real Payment</a>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div></div>";

// Step 3: Payment Flow Status
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-flow-chart me-2'></i>Payment Flow Status</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>✅ Fixed Payment Issues:</h5>";
echo "<ul class='list-group list-group-flush'>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Enhanced Flutterwave API verification</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Multiple success status handling</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Better error logging and debugging</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Improved callback handling</li>";
echo "<li class='list-group-item'><i class='fas fa-check text-success me-2'></i>Test payment system for development</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>🔄 Payment Flow Steps:</h5>";
echo "<ol class='list-group list-group-numbered'>";
echo "<li class='list-group-item'>User clicks 'Pay Now' button</li>";
echo "<li class='list-group-item'>Flutterwave checkout opens</li>";
echo "<li class='list-group-item'>User completes payment</li>";
echo "<li class='list-group-item'>Callback verifies transaction</li>";
echo "<li class='list-group-item'>Property marked as sold</li>";
echo "<li class='list-group-item'>Messages sent to buyer/seller</li>";
echo "<li class='list-group-item'>Success page displayed</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Step 4: Testing Instructions
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'><i class='fas fa-clipboard-list me-2'></i>Testing Instructions</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>🧪 Test Payment (Recommended):</h5>";
echo "<ol>";
echo "<li>Login as a buyer account</li>";
echo "<li>Visit an available property page</li>";
echo "<li>Click 'Test Payment' button</li>";
echo "<li>Verify property status changes to 'Sold'</li>";
echo "<li>Check property disappears from listings</li>";
echo "<li>Verify messages sent to buyer/seller</li>";
echo "</ol>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>💳 Real Flutterwave Payment:</h5>";
echo "<ol>";
echo "<li>Login as a buyer account</li>";
echo "<li>Visit an available property page</li>";
echo "<li>Click 'Pay Now' button</li>";
echo "<li>Complete Flutterwave payment</li>";
echo "<li>Verify enhanced verification works</li>";
echo "<li>Check success page displays</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h6><i class='fas fa-info-circle me-2'></i>Debugging Information:</h6>";
echo "<ul class='mb-0'>";
echo "<li>Payment verification responses are logged to PHP error log</li>";
echo "<li>Check browser console for JavaScript callback data</li>";
echo "<li>Use test payment system to verify flow without API calls</li>";
echo "<li>Monitor database changes in real-time</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

// Step 5: Database Status Check
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Status Check</h3>";
echo "</div>";
echo "<div class='card-body'>";

$totalProperties = $conn->query("SELECT COUNT(*) as count FROM properties")->fetch_assoc()['count'];
$availableCount = $conn->query("SELECT COUNT(*) as count FROM properties WHERE status = 'Available'")->fetch_assoc()['count'];
$soldCount = $conn->query("SELECT COUNT(*) as count FROM properties WHERE status = 'Sold'")->fetch_assoc()['count'];
$totalPayments = $conn->query("SELECT COUNT(*) as count FROM payments WHERE status = 'completed'")->fetch_assoc()['count'];

echo "<div class='row text-center'>";
echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-primary'>$totalProperties</h3>";
echo "<p class='mb-0'>Total Properties</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-success'>$availableCount</h3>";
echo "<p class='mb-0'>Available</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-danger'>$soldCount</h3>";
echo "<p class='mb-0'>Sold</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body'>";
echo "<h3 class='text-info'>$totalPayments</h3>";
echo "<p class='mb-0'>Completed Payments</p>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Action Buttons
echo "<div class='text-center'>";
echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='index.php' class='btn btn-primary' target='_blank'><i class='fas fa-home me-2'></i>Test Homepage</a>";
echo "<a href='search.php' class='btn btn-info' target='_blank'><i class='fas fa-search me-2'></i>Test Search</a>";
echo "<a href='login.php' class='btn btn-success'><i class='fas fa-sign-in-alt me-2'></i>Login as Buyer</a>";
echo "<a href='my_paid_properties.php' class='btn btn-warning' target='_blank'><i class='fas fa-list me-2'></i>My Purchases</a>";
echo "</div>";
echo "</div>";

// Final Status
echo "<div class='text-center mt-4'>";
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>✅ PAYMENT INTEGRATION FIXES COMPLETE!</h4>";
echo "<p class='mb-0'>Enhanced Flutterwave verification, better error handling, and test payment system implemented.</p>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
