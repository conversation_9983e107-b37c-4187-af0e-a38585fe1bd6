<?php
// Simple Rental Payment - Simplified rental payment processing for testing
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/rental_functions.php';

// Check if user is logged in and is a buyer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    die('Not authorized.');
}

// Get parameters from URL
$transactionId = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : 'RENT_' . time();
$propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;
$tenantId = isset($_GET['tenant_id']) ? (int)$_GET['tenant_id'] : $_SESSION['user_id'];
$landlordId = isset($_GET['landlord_id']) ? (int)$_GET['landlord_id'] : 0;
$monthlyRate = isset($_GET['monthly_rate']) ? floatval($_GET['monthly_rate']) : 0;
$duration = isset($_GET['duration']) ? (int)$_GET['duration'] : 0;
$totalAmount = isset($_GET['total_amount']) ? floatval($_GET['total_amount']) : 0;
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d');
$landlordName = isset($_GET['landlord_name']) ? $_GET['landlord_name'] : '';

// Validate required parameters
if (!$propertyId || !$tenantId || !$landlordId || !$monthlyRate || !$duration || !$totalAmount) {
    die('Missing required rental payment parameters');
}

// Get property details
$property = getPropertyById($propertyId);
if (!$property || $property['listing_type'] !== 'Rent') {
    die('Invalid property for rental');
}

$propertyTitle = $property['title'];
$tenantName = $_SESSION['username'];

// Calculate rental end date
$endDate = calculateRentalEndDate($startDate, $duration);

try {
    // Check if rental tables exist
    $rentalPaymentsExists = $conn->query("SHOW TABLES LIKE 'rental_payments'")->num_rows > 0;
    $propertyRentalsExists = $conn->query("SHOW TABLES LIKE 'property_rentals'")->num_rows > 0;
    
    if (!$rentalPaymentsExists || !$propertyRentalsExists) {
        throw new Exception("Rental system tables do not exist. Please run the setup first.");
    }
    
    // Start database transaction
    $conn->begin_transaction();
    
    // Insert rental payment record
    $sql = "INSERT INTO rental_payments (property_id, tenant_id, landlord_id, monthly_rate, rent_duration, total_amount, rental_start_date, rental_end_date, transaction_id, payment_method) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Flutterwave')";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Failed to prepare rental payment statement: " . $conn->error);
    }
    
    $stmt->bind_param('iiididsss', $propertyId, $tenantId, $landlordId, $monthlyRate, $duration, $totalAmount, $startDate, $endDate, $transactionId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert rental payment: " . $stmt->error);
    }
    
    $rentalPaymentId = $conn->insert_id;
    
    // Check if property already has an active rental
    $existingRentalCheck = $conn->query("SELECT COUNT(*) as count FROM property_rentals WHERE property_id = $propertyId AND status = 'active' AND rental_end_date >= CURDATE()")->fetch_assoc();
    
    if ($existingRentalCheck['count'] > 0) {
        // Update existing rental to terminated
        $sql = "UPDATE property_rentals SET status = 'terminated' WHERE property_id = ? AND status = 'active'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $propertyId);
        $stmt->execute();
    }
    
    // Insert new active rental
    $sql = "INSERT INTO property_rentals (property_id, tenant_id, landlord_id, rental_payment_id, rental_start_date, rental_end_date, monthly_rate, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 'active')";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Failed to prepare property rental statement: " . $conn->error);
    }
    
    $stmt->bind_param('iiiissd', $propertyId, $tenantId, $landlordId, $rentalPaymentId, $startDate, $endDate, $monthlyRate);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert property rental: " . $stmt->error);
    }
    
    // Send rental confirmation messages
    $rentalDetails = [
        'monthly_rate' => $monthlyRate,
        'duration' => $duration,
        'total_amount' => $totalAmount,
        'start_date' => $startDate,
        'end_date' => $endDate,
        'transaction_id' => $transactionId
    ];
    
    sendRentalConfirmationMessages($propertyId, $tenantId, $landlordId, $rentalDetails);
    
    // Commit transaction
    $conn->commit();
    
    // Success! Show success page
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Rental Payment Successful</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card shadow-lg">
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h2 class="text-success mb-3">Rental Payment Successful!</h2>
                            <h4 class="mb-4">🏠 Congratulations on your new rental!</h4>
                            
                            <div class="alert alert-success">
                                <h5 class="mb-3">Rental Agreement Details:</h5>
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Property:</strong></div>
                                            <div class="col-sm-7"><?php echo htmlspecialchars($propertyTitle); ?></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Monthly Rate:</strong></div>
                                            <div class="col-sm-7">TZS <?php echo number_format($monthlyRate, 2); ?></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Duration:</strong></div>
                                            <div class="col-sm-7"><?php echo $duration; ?> months</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Total Paid:</strong></div>
                                            <div class="col-sm-7 text-success"><strong>TZS <?php echo number_format($totalAmount, 2); ?></strong></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>Start Date:</strong></div>
                                            <div class="col-sm-7"><?php echo date('M j, Y', strtotime($startDate)); ?></div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-sm-5"><strong>End Date:</strong></div>
                                            <div class="col-sm-7"><?php echo date('M j, Y', strtotime($endDate)); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-start">
                                    <div class="col-sm-4"><strong>Transaction ID:</strong></div>
                                    <div class="col-sm-8"><code><?php echo $transactionId; ?></code></div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>What happens next?</h6>
                                <ul class="text-start mb-0">
                                    <li>The property is now marked as <strong>RENTED</strong> for your rental period</li>
                                    <li>You and the landlord have received confirmation messages</li>
                                    <li>The landlord will contact you for key handover and move-in procedures</li>
                                    <li>Check your inbox for detailed rental agreement confirmation</li>
                                    <li>Your rental period starts on <strong><?php echo date('M j, Y', strtotime($startDate)); ?></strong></li>
                                </ul>
                            </div>
                            
                            <div class="d-flex gap-3 justify-content-center flex-wrap mt-4">
                                <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>View Property
                                </a>
                                <a href="my_rentals.php" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>My Rentals
                                </a>
                                <a href="inbox.php" class="btn btn-info">
                                    <i class="fas fa-envelope me-2"></i>Check Messages
                                </a>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-search me-2"></i>Browse More Properties
                                </a>
                            </div>
                            
                            <p class="text-muted mt-4">This window will close automatically in <span id="countdown">10</span> seconds.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            let countdown = 10;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(function() {
                countdown--;
                countdownElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    if (window.opener) {
                        window.opener.location.href = 'my_rentals.php?rental_success=1&property_id=<?php echo $propertyId; ?>';
                    }
                    window.close();
                }
            }, 1000);
        </script>
    </body>
    </html>
    <?php
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Rental Payment Error</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-times-circle text-danger" style="font-size: 3rem;"></i>
                            <h3 class="text-danger mt-3">Rental Payment Processing Error</h3>
                            <p class="text-muted"><?php echo htmlspecialchars($e->getMessage()); ?></p>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Possible Solutions:</h6>
                                <ul class="text-start mb-0">
                                    <li>Ensure rental database tables are set up properly</li>
                                    <li>Check that all required parameters are provided</li>
                                    <li>Verify property exists and is available for rent</li>
                                    <li>Try the rental payment again</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex gap-3 justify-content-center">
                                <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-primary">Back to Property</a>
                                <a href="quick_setup_rental.php" class="btn btn-warning">Setup Rental System</a>
                                <a href="test_rental_system.php" class="btn btn-info">Test System</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

$conn->close();
?>
