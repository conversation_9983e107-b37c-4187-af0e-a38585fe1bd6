<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TX Properties - Style Test</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Test Navigation -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">TX PROPERTIES</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="login.php">Login</a>
                <a class="nav-link" href="register.php">Register</a>
            </div>
        </div>
    </nav>

    <!-- Test Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h1 class="fade-in">CSS Test - Modern Styling</h1>
                    <p class="fade-in fade-in-delay-1">
                        If you can see this with modern styling, the CSS is working correctly.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Search Form -->
    <div class="container">
        <div class="search-form mb-5">
            <h3>Search Form Test</h3>
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="location" class="form-label">Location</label>
                    <select class="form-select" id="location">
                        <option value="">All Locations</option>
                        <option value="Dar es Salaam">Dar es Salaam</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary btn-search">
                        <i class="fas fa-search me-2"></i> Search
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Property Card -->
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <div class="card property-card">
                    <img src="https://via.placeholder.com/300x200" class="card-img-top property-img" alt="Test Property">
                    <div class="card-body">
                        <h5 class="property-title">Test Property</h5>
                        <p class="property-price">$100,000</p>
                        <p class="property-location">
                            <i class="fas fa-map-marker-alt"></i> Test Location
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS Variables Test -->
    <div class="container mt-5">
        <div class="alert alert-info">
            <h4>CSS Variables Test</h4>
            <p>Primary Color: <span style="color: var(--primary-color); font-weight: bold;">This should be dark green</span></p>
            <p>Secondary Color: <span style="color: var(--secondary-color); font-weight: bold;">This should be terracotta</span></p>
            <p>Background: <span style="background-color: var(--beige-light); padding: 5px;">This should have beige background</span></p>
        </div>
    </div>

    <!-- JavaScript Test -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            // Test CSS variables
            const root = getComputedStyle(document.documentElement);
            const primaryColor = root.getPropertyValue('--primary-color').trim();
            const secondaryColor = root.getPropertyValue('--secondary-color').trim();
            
            console.log('Primary color:', primaryColor);
            console.log('Secondary color:', secondaryColor);
            
            if (primaryColor && secondaryColor) {
                console.log('✅ CSS variables are working!');
            } else {
                console.log('❌ CSS variables not found');
            }
        });
    </script>
</body>
</html>
