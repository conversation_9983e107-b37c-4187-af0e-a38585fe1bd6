<?php
// Verify Property Features Implementation
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Verify Property Features Implementation</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>.feature-yes{color:#28a745;} .feature-no{color:#6c757d;}</style>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🏠 Property Features Implementation Verification</h1>";

// Step 1: Check Database Structure
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Structure Check</h3>";
echo "</div>";
echo "<div class='card-body'>";

$result = $conn->query("DESCRIBE properties");
$existingColumns = [];
while ($row = $result->fetch_assoc()) {
    $existingColumns[] = $row['Field'];
}

$requiredFeatures = [
    'has_electricity' => 'Electricity availability',
    'has_water' => 'Water supply availability', 
    'has_fence' => 'Property fencing',
    'has_gym' => 'Gym facilities',
    'has_air_condition' => 'Air conditioning',
    'has_security' => 'Security features',
    'has_pool' => 'Swimming pool'
];

$allExist = true;
echo "<div class='row'>";
foreach ($requiredFeatures as $column => $description) {
    $exists = in_array($column, $existingColumns);
    if (!$exists) $allExist = false;
    
    echo "<div class='col-md-6 mb-2'>";
    echo "<div class='d-flex align-items-center'>";
    echo $exists ? "<i class='fas fa-check-circle text-success me-2'></i>" : "<i class='fas fa-times-circle text-danger me-2'></i>";
    echo "<span>$column</span>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

if ($allExist) {
    echo "<div class='alert alert-success mt-3'>";
    echo "<i class='fas fa-check-circle me-2'></i>All feature columns exist in the database!";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning mt-3'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>Some feature columns are missing. Please run the SQL script: <code>add_property_features_columns.sql</code>";
    echo "</div>";
}

echo "</div></div>";

// Step 2: Test Data Retrieval
if ($allExist) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h3 class='mb-0'><i class='fas fa-search me-2'></i>Data Retrieval Test</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    $testQuery = "SELECT id, title, has_electricity, has_water, has_fence, has_gym, has_air_condition, has_security, has_pool FROM properties LIMIT 3";
    $testResult = $conn->query($testQuery);
    
    if ($testResult && $testResult->num_rows > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>ID</th><th>Title</th><th>Electricity</th><th>Water</th><th>Fence</th><th>Gym</th><th>AC</th><th>Security</th><th>Pool</th></tr>";
        echo "</thead><tbody>";
        
        while ($row = $testResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['title'], 0, 30)) . "...</td>";
            
            foreach (['has_electricity', 'has_water', 'has_fence', 'has_gym', 'has_air_condition', 'has_security', 'has_pool'] as $feature) {
                $value = $row[$feature];
                $icon = $value ? "<i class='fas fa-check text-success'></i>" : "<i class='fas fa-times text-muted'></i>";
                echo "<td class='text-center'>$icon</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle me-2'></i>Data retrieved successfully! Features are being fetched from the database.";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>No properties found or query failed.";
        echo "</div>";
    }
    
    echo "</div></div>";
    
    // Step 3: Test Property Display
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h3 class='mb-0'><i class='fas fa-eye me-2'></i>Property Display Preview</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    $sampleQuery = "SELECT * FROM properties WHERE id = 1 LIMIT 1";
    $sampleResult = $conn->query($sampleQuery);
    
    if ($sampleResult && $sampleResult->num_rows > 0) {
        $property = $sampleResult->fetch_assoc();
        
        echo "<h5>Property: " . htmlspecialchars($property['title']) . "</h5>";
        echo "<div class='row mt-3'>";
        echo "<div class='col-md-6'>";
        echo "<h6>Additional Features:</h6>";
        echo "<ul class='list-group list-group-flush'>";
        
        $featureDisplay = [
            'has_electricity' => ['icon' => 'fas fa-bolt text-warning', 'name' => 'Electricity'],
            'has_water' => ['icon' => 'fas fa-tint text-info', 'name' => 'Water'],
            'has_fence' => ['icon' => 'fas fa-border-all text-secondary', 'name' => 'Fence'],
            'has_security' => ['icon' => 'fas fa-shield-alt text-success', 'name' => 'Security']
        ];
        
        foreach ($featureDisplay as $feature => $display) {
            $hasFeature = isset($property[$feature]) && $property[$feature];
            $status = $hasFeature ? 'Yes' : 'No';
            $badgeClass = $hasFeature ? 'bg-success' : 'bg-secondary';
            
            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
            echo "<span><i class='{$display['icon']} me-2'></i> {$display['name']}</span>";
            echo "<span class='badge $badgeClass'>$status</span>";
            echo "</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='col-md-6'>";
        echo "<h6>More Features:</h6>";
        echo "<ul class='list-group list-group-flush'>";
        
        $moreFeatures = [
            'has_gym' => ['icon' => 'fas fa-dumbbell text-primary', 'name' => 'Gym'],
            'has_air_condition' => ['icon' => 'fas fa-snowflake text-info', 'name' => 'Air Conditioning'],
            'has_pool' => ['icon' => 'fas fa-swimming-pool text-primary', 'name' => 'Swimming Pool']
        ];
        
        foreach ($moreFeatures as $feature => $display) {
            $hasFeature = isset($property[$feature]) && $property[$feature];
            $status = $hasFeature ? 'Yes' : 'No';
            $badgeClass = $hasFeature ? 'bg-success' : 'bg-secondary';
            
            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
            echo "<span><i class='{$display['icon']} me-2'></i> {$display['name']}</span>";
            echo "<span class='badge $badgeClass'>$status</span>";
            echo "</li>";
        }
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='alert alert-success mt-3'>";
        echo "<i class='fas fa-check-circle me-2'></i>Property features are displaying correctly!";
        echo "</div>";
    }
    
    echo "</div></div>";
}

// Step 4: Action Buttons
echo "<div class='card'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-tools me-2'></i>Actions</h3>";
echo "</div>";
echo "<div class='card-body text-center'>";

if (!$allExist) {
    echo "<div class='alert alert-warning'>";
    echo "<h5>⚠️ Database Setup Required</h5>";
    echo "<p>Please run the SQL script to add missing columns:</p>";
    echo "<ol class='text-start'>";
    echo "<li>Open phpMyAdmin</li>";
    echo "<li>Select your 'txproperties' database</li>";
    echo "<li>Go to SQL tab</li>";
    echo "<li>Copy and paste the contents of <code>add_property_features_columns.sql</code></li>";
    echo "<li>Click 'Go' to execute</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='property.php?id=1' class='btn btn-primary'><i class='fas fa-eye me-2'></i>View Property Page</a>";
echo "<a href='add_property.php' class='btn btn-success'><i class='fas fa-plus me-2'></i>Add New Property</a>";
echo "<a href='debug_property_features.php' class='btn btn-info'><i class='fas fa-bug me-2'></i>Debug Features</a>";
echo "<a href='index.php' class='btn btn-secondary'><i class='fas fa-home me-2'></i>Go to Homepage</a>";
echo "</div>";

echo "</div></div>";

// Final Status
echo "<div class='text-center mt-4'>";
if ($allExist) {
    echo "<div class='alert alert-success'>";
    echo "<h4><i class='fas fa-check-circle me-2'></i>✅ PROPERTY FEATURES FULLY IMPLEMENTED!</h4>";
    echo "<p class='mb-0'>Database columns exist, data is being retrieved, and features are displaying correctly.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>⚠️ SETUP REQUIRED</h4>";
    echo "<p class='mb-0'>Please add the missing database columns using the SQL script provided.</p>";
    echo "</div>";
}
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
