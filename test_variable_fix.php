<?php
// Test Variable Fix - Verify no undefined variable errors
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Variable Fix</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔧 Variable Fix Verification</h1>";

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-check me-2'></i>Fix Applied</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>❌ Before (Causing Errors):</h5>";
echo "<pre class='bg-light p-3 border rounded'>";
echo "// Variables defined in mobile section only\n";
echo "<!-- Payment Section -->\n";
echo "<?php\n";
echo "\$paymentStatus = canUserPayForProperty(\$property);\n";
echo "\$existingPayment = getPropertyPaymentStatus(\$property['id']);\n";
echo "?>\n\n";
echo "// Desktop sidebar tries to use undefined variables\n";
echo "<?php if (\$paymentStatus['can_pay']): ?> // ERROR!";
echo "</pre>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>✅ After (Fixed):</h5>";
echo "<pre class='bg-light p-3 border rounded'>";
echo "// Variables defined globally after property load\n";
echo "\$formattedPrice = formatPrice(\$property['price']);\n\n";
echo "// Check payment status (used throughout page)\n";
echo "\$paymentStatus = canUserPayForProperty(\$property);\n";
echo "\$existingPayment = getPropertyPaymentStatus(\$property['id']);\n\n";
echo "// Now available in both desktop and mobile sections\n";
echo "<?php if (\$paymentStatus['can_pay']): ?> // OK!";
echo "</pre>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-success mt-3'>";
echo "<h5><i class='fas fa-check-circle me-2'></i>Fix Summary:</h5>";
echo "<ul class='mb-0'>";
echo "<li>Moved <code>\$paymentStatus</code> and <code>\$existingPayment</code> variable definitions to global scope</li>";
echo "<li>Variables now defined after property is loaded (line ~34)</li>";
echo "<li>Available to both desktop sidebar and mobile payment sections</li>";
echo "<li>Removed duplicate variable definitions from mobile section</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-test-tube me-2'></i>Test the Fix</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<p>Visit these property pages to verify no more undefined variable warnings:</p>";

$testProperties = [1, 2, 3];
echo "<div class='row'>";
foreach ($testProperties as $propertyId) {
    echo "<div class='col-md-4 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-body text-center'>";
    echo "<h6>Property $propertyId</h6>";
    echo "<a href='property.php?id=$propertyId' class='btn btn-primary btn-sm' target='_blank'>";
    echo "<i class='fas fa-external-link-alt me-2'></i>Test Property $propertyId";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle me-2'></i>What to Check:</h5>";
echo "<ul class='mb-0'>";
echo "<li>No PHP warnings about undefined variables</li>";
echo "<li>Pay Now button displays correctly in desktop sidebar</li>";
echo "<li>Payment section displays correctly in mobile view</li>";
echo "<li>Payment status messages show appropriately</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-code me-2'></i>Code Changes Made</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>1. Added Global Variable Definitions (property.php ~line 34):</h5>";
echo "<pre class='bg-light p-3 border rounded'>";
echo "// Format price\n";
echo "\$formattedPrice = formatPrice(\$property['price'], \$property['listing_type']);\n\n";
echo "// Check payment status and user eligibility (used throughout the page)\n";
echo "\$paymentStatus = canUserPayForProperty(\$property);\n";
echo "\$existingPayment = getPropertyPaymentStatus(\$property['id']);";
echo "</pre>";

echo "<h5>2. Removed Duplicate Definitions (property.php mobile section):</h5>";
echo "<pre class='bg-light p-3 border rounded'>";
echo "<!-- Payment Section -->\n";
echo "<!-- Removed duplicate variable definitions -->";
echo "</pre>";

echo "<div class='alert alert-success'>";
echo "<i class='fas fa-check-circle me-2'></i><strong>Result:</strong> Variables are now available throughout the entire property.php file without duplication.";
echo "</div>";

echo "</div></div>";

echo "<div class='text-center'>";
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>✅ UNDEFINED VARIABLE ERRORS FIXED!</h4>";
echo "<p class='mb-0'>Payment status variables are now properly defined and accessible throughout the property page.</p>";
echo "</div>";

echo "<div class='d-flex gap-3 justify-content-center flex-wrap'>";
echo "<a href='property.php?id=1' class='btn btn-primary' target='_blank'><i class='fas fa-eye me-2'></i>Test Property 1</a>";
echo "<a href='property.php?id=2' class='btn btn-primary' target='_blank'><i class='fas fa-eye me-2'></i>Test Property 2</a>";
echo "<a href='property.php?id=3' class='btn btn-primary' target='_blank'><i class='fas fa-eye me-2'></i>Test Property 3</a>";
echo "<a href='test_pay_now_button.php' class='btn btn-success'><i class='fas fa-cog me-2'></i>Full Payment Test</a>";
echo "</div>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

$conn->close();
?>
