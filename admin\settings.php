<?php
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}
require_once '../includes/config.php';
include 'admin_header.php';

// Try to fetch settings
$site_name = '';
$contact_email = '';
$settings_exist = false;

if ($result = $conn->query("SHOW TABLES LIKE 'settings'")) {
    if ($result->num_rows > 0) {
        $settings_exist = true;
        $row = $conn->query("SELECT * FROM settings LIMIT 1")->fetch_assoc();
        $site_name = $row['site_name'] ?? '';
        $contact_email = $row['contact_email'] ?? '';
    }
}

$admin_id = $_SESSION['admin_id'];
$admin_email = '';
$admin_username = $_SESSION['admin_username'];
$admin_error = '';
$admin_success = '';

// Fetch current admin email
$sql = "SELECT email FROM users WHERE id = ? AND role = 'admin'";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $admin_id);
$stmt->execute();
$stmt->bind_result($admin_email);
$stmt->fetch();
$stmt->close();

// Handle admin password/email change
if (isset($_POST['change_admin_settings'])) {
    $new_email = trim($_POST['admin_email']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Fetch current password hash
    $sql = "SELECT password FROM users WHERE id = ? AND role = 'admin'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $admin_id);
    $stmt->execute();
    $stmt->bind_result($current_hash);
    $stmt->fetch();
    $stmt->close();

    if (!password_verify($current_password, $current_hash)) {
        $admin_error = 'Current password is incorrect.';
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $admin_error = 'New passwords do not match.';
    } else {
        // Update email
        $sql = "UPDATE users SET email = ? WHERE id = ? AND role = 'admin'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('si', $new_email, $admin_id);
        $stmt->execute();
        $stmt->close();
        $_SESSION['email'] = $new_email;
        $admin_success = 'Email updated successfully.';
        // Update password if provided
        if (!empty($new_password)) {
            $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ? WHERE id = ? AND role = 'admin'";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('si', $new_hash, $admin_id);
            $stmt->execute();
            $stmt->close();
            $admin_success .= ' Password updated successfully.';
        }
        // Refresh admin email
        $admin_email = $new_email;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $settings_exist && isset($_POST['site_name'])) {
    $site_name = trim($_POST['site_name']);
    $contact_email = trim($_POST['contact_email']);
    $conn->query("UPDATE settings SET site_name = '" . $conn->real_escape_string($site_name) . "', contact_email = '" . $conn->real_escape_string($contact_email) . "' LIMIT 1");
    echo '<div class="alert alert-success">Settings updated.</div>';
}
?>
<div class="container-fluid">
    <h1 class="mb-4">Site Settings</h1>
    <?php if (!$settings_exist): ?>
        <div class="alert alert-warning">Settings table or fields not found. Please create a <code>settings</code> table with <code>site_name</code> and <code>contact_email</code> fields.</div>
    <?php else: ?>
    <form method="post" class="mb-4" style="max-width: 500px;">
        <div class="mb-3">
            <label for="site_name" class="form-label">Site Name</label>
            <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($site_name); ?>" required>
        </div>
        <div class="mb-3">
            <label for="contact_email" class="form-label">Contact Email</label>
            <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($contact_email); ?>" required>
        </div>
        <button type="submit" class="btn btn-primary">Save Settings</button>
    </form>
    <?php endif; ?>

    <h2 class="mb-4 mt-5">Admin Account Settings</h2>
    <?php if ($admin_error): ?>
        <div class="alert alert-danger"><?php echo $admin_error; ?></div>
    <?php endif; ?>
    <?php if ($admin_success): ?>
        <div class="alert alert-success"><?php echo $admin_success; ?></div>
    <?php endif; ?>
    <form method="post" style="max-width: 500px;">
        <div class="mb-3">
            <label for="admin_email" class="form-label">Admin Email</label>
            <input type="email" class="form-control" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($admin_email); ?>" required>
        </div>
        <div class="mb-3">
            <label for="current_password" class="form-label">Current Password</label>
            <input type="password" class="form-control" id="current_password" name="current_password" required>
        </div>
        <div class="mb-3">
            <label for="new_password" class="form-label">New Password</label>
            <input type="password" class="form-control" id="new_password" name="new_password">
            <div class="form-text">Leave blank if you do not want to change your password.</div>
        </div>
        <div class="mb-3">
            <label for="confirm_password" class="form-label">Confirm New Password</label>
            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
        </div>
        <button type="submit" name="change_admin_settings" class="btn btn-primary">Update Admin Account</button>
    </form>
</div>
<?php include 'admin_footer.php'; ?> 