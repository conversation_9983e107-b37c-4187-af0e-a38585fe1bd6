<?php
// Rental Functions - Helper functions for rental property management

/**
 * Check if a property is currently rented
 * @param int $propertyId
 * @return array|false Returns rental info if rented, false if available
 */
function isPropertyRented($propertyId) {
    global $conn;
    
    $sql = "SELECT pr.*, u.username as tenant_name, u.email as tenant_email 
            FROM property_rentals pr 
            JOIN users u ON pr.tenant_id = u.id 
            WHERE pr.property_id = ? AND pr.status = 'active' AND pr.rental_end_date >= CURDATE()";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $propertyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    return false;
}

/**
 * Get rental payment options (durations)
 * @return array
 */
function getRentalDurations() {
    return [
        1 => '1 Month',
        3 => '3 Months',
        6 => '6 Months',
        12 => '1 Year',
        24 => '2 Years'
    ];
}

/**
 * Calculate total rent amount
 * @param float $monthlyRate
 * @param int $duration Duration in months
 * @return float
 */
function calculateTotalRent($monthlyRate, $duration) {
    $total = $monthlyRate * $duration;
    
    // Apply discounts for longer durations
    if ($duration >= 12) {
        $total = $total * 0.95; // 5% discount for 1+ years
    } elseif ($duration >= 6) {
        $total = $total * 0.97; // 3% discount for 6+ months
    }
    
    return round($total, 2);
}

/**
 * Get rental end date based on start date and duration
 * @param string $startDate
 * @param int $duration Duration in months
 * @return string
 */
function calculateRentalEndDate($startDate, $duration) {
    $date = new DateTime($startDate);
    $date->add(new DateInterval("P{$duration}M"));
    return $date->format('Y-m-d');
}

/**
 * Process rental payment
 * @param int $propertyId
 * @param int $tenantId
 * @param int $landlordId
 * @param float $monthlyRate
 * @param int $duration
 * @param float $totalAmount
 * @param string $transactionId
 * @param string $startDate
 * @return bool
 */
function processRentalPayment($propertyId, $tenantId, $landlordId, $monthlyRate, $duration, $totalAmount, $transactionId, $startDate = null) {
    global $conn;
    
    if (!$startDate) {
        $startDate = date('Y-m-d');
    }
    
    $endDate = calculateRentalEndDate($startDate, $duration);
    
    try {
        $conn->begin_transaction();
        
        // Insert rental payment record
        $sql = "INSERT INTO rental_payments (property_id, tenant_id, landlord_id, monthly_rate, rent_duration, total_amount, rental_start_date, rental_end_date, transaction_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiiddsss', $propertyId, $tenantId, $landlordId, $monthlyRate, $duration, $totalAmount, $startDate, $endDate, $transactionId);
        $stmt->execute();
        
        $rentalPaymentId = $conn->insert_id;
        
        // Check if property already has an active rental
        $existingRental = isPropertyRented($propertyId);
        if ($existingRental) {
            // Update existing rental to terminated
            $sql = "UPDATE property_rentals SET status = 'terminated' WHERE property_id = ? AND status = 'active'";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('i', $propertyId);
            $stmt->execute();
        }
        
        // Insert new active rental
        $sql = "INSERT INTO property_rentals (property_id, tenant_id, landlord_id, rental_payment_id, rental_start_date, rental_end_date, monthly_rate) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiissd', $propertyId, $tenantId, $landlordId, $rentalPaymentId, $startDate, $endDate, $monthlyRate);
        $stmt->execute();
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Rental payment processing error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send rental confirmation messages
 * @param int $propertyId
 * @param int $tenantId
 * @param int $landlordId
 * @param array $rentalDetails
 * @return bool
 */
function sendRentalConfirmationMessages($propertyId, $tenantId, $landlordId, $rentalDetails) {
    global $conn;
    
    // Get property and user details
    $property = getPropertyById($propertyId);
    $tenant = getUserById($tenantId);
    $landlord = getUserById($landlordId);
    
    if (!$property || !$tenant || !$landlord) {
        return false;
    }
    
    $propertyTitle = $property['title'];
    $tenantName = $tenant['username'];
    $landlordName = $landlord['username'];
    
    // Message to Tenant
    $tenantMsg = "Congratulations! You have successfully rented the property titled \"$propertyTitle\".

Rental Details:
- Property: $propertyTitle (ID: $propertyId)
- Monthly Rate: TZS " . number_format($rentalDetails['monthly_rate'], 2) . "
- Rental Duration: {$rentalDetails['duration']} months
- Total Amount Paid: TZS " . number_format($rentalDetails['total_amount'], 2) . "
- Rental Period: {$rentalDetails['start_date']} to {$rentalDetails['end_date']}
- Transaction ID: {$rentalDetails['transaction_id']}
- Payment Method: Flutterwave

Your rental period starts on {$rentalDetails['start_date']}. Please coordinate with the landlord for key handover and move-in procedures.

Best regards,
TX Properties Team";

    $tenantSubject = "Rental Confirmation - $propertyTitle";
    
    // Message to Landlord
    $landlordMsg = "Great news! Your property titled \"$propertyTitle\" has been rented.

Rental Details:
- Property: $propertyTitle (ID: $propertyId)
- Tenant: $tenantName
- Monthly Rate: TZS " . number_format($rentalDetails['monthly_rate'], 2) . "
- Rental Duration: {$rentalDetails['duration']} months
- Total Amount Received: TZS " . number_format($rentalDetails['total_amount'], 2) . "
- Rental Period: {$rentalDetails['start_date']} to {$rentalDetails['end_date']}
- Transaction ID: {$rentalDetails['transaction_id']}
- Payment Method: Flutterwave

The rental payment has been processed successfully. Please coordinate with the tenant for key handover and move-in procedures.

Congratulations on your successful rental!

Best regards,
TX Properties Team";

    $landlordSubject = "Property Rented - $propertyTitle";
    
    try {
        // Send message to tenant
        $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiss', $landlordId, $tenantId, $propertyId, $tenantSubject, $tenantMsg);
        $stmt->execute();
        
        // Send message to landlord
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiss', $tenantId, $landlordId, $propertyId, $landlordSubject, $landlordMsg);
        $stmt->execute();
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error sending rental confirmation messages: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user by ID
 * @param int $userId
 * @return array|false
 */
function getUserById($userId) {
    global $conn;
    
    $sql = "SELECT * FROM users WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    return false;
}

/**
 * Get tenant's rental history
 * @param int $tenantId
 * @return array
 */
function getTenantRentalHistory($tenantId) {
    global $conn;
    
    $sql = "SELECT rp.*, p.title as property_title, p.location, u.username as landlord_name 
            FROM rental_payments rp 
            JOIN properties p ON rp.property_id = p.id 
            JOIN users u ON rp.landlord_id = u.id 
            WHERE rp.tenant_id = ? 
            ORDER BY rp.payment_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $tenantId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $rentals = [];
    while ($row = $result->fetch_assoc()) {
        $rentals[] = $row;
    }
    
    return $rentals;
}

/**
 * Get landlord's rental income
 * @param int $landlordId
 * @return array
 */
function getLandlordRentalIncome($landlordId) {
    global $conn;
    
    $sql = "SELECT rp.*, p.title as property_title, p.location, u.username as tenant_name 
            FROM rental_payments rp 
            JOIN properties p ON rp.property_id = p.id 
            JOIN users u ON rp.tenant_id = u.id 
            WHERE rp.landlord_id = ? 
            ORDER BY rp.payment_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $landlordId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $rentals = [];
    while ($row = $result->fetch_assoc()) {
        $rentals[] = $row;
    }
    
    return $rentals;
}

/**
 * Update expired rentals
 * This function should be called periodically (e.g., via cron job)
 */
function updateExpiredRentals() {
    global $conn;
    
    $sql = "UPDATE property_rentals SET status = 'expired' WHERE rental_end_date < CURDATE() AND status = 'active'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    return $stmt->affected_rows;
}
?>
