<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/validation.php';

// Initialize variables
$email = '';
$password = '';
$error = '';
$success = '';

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];

    // Enhanced validation
    $errors = [];

    // Validate email
    $emailValidation = validateEmail($email);
    if (!$emailValidation['valid']) {
        $errors[] = $emailValidation['message'];
    }

    // Basic password check (not empty)
    if (empty($password)) {
        $errors[] = "Password is required.";
    }

    if (empty($errors)) {
        // Check if user exists
        $sql = "SELECT * FROM users WHERE email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            
            // Verify password (using password_verify() in a real app)
            // This is a simplified version for demo purposes
            // In real application, use: if (password_verify($password, $user['password']))
            if ($password === 'password' || $user['password'] === $password) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['user_role'] = $user['role'];
                
                // Redirect based on user role
                if ($user['role'] === 'admin') {
                    redirect('admin/index.php');
                } else {
                    redirect('index.php');
                }
            } else {
                $errors[] = "Invalid password. Please try again.";
            }
        } else {
            $errors[] = "No account found with that email address.";
        }
    }

    // Convert errors array to single error message for display
    if (!empty($errors)) {
        $error = implode('<br>', $errors);
    }
}
?>

<!-- Add auth page styling -->
<style>
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    min-height: 100vh;
}
</style>

<!-- Login Page Content -->
<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100 py-5">
    <div class="row w-100 justify-content-center">
        <div class="col-md-8 col-lg-6 col-xl-4">
            <div class="card shadow-lg border-0" style="border-radius: 15px; overflow: hidden;">
                <div class="card-header text-center py-4" style="background: linear-gradient(135deg, #2c5530 0%, #34495e 100%); border: none;">
                    <h4 class="mb-0 text-white fw-bold">Login to Your Account</h4>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    
                    <form action="login.php" method="post" novalidate>
                        <div class="mb-3">
                            <label for="email" class="form-label fw-semibold text-dark">Email Address</label>
                            <input type="email" class="form-control form-control-lg" id="email" name="email"
                                   value="<?php echo htmlspecialchars($email); ?>" required
                                   style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px; font-size: 16px; min-width: 100%;">
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label fw-semibold text-dark">Password</label>
                            <div class="password-field-wrapper">
                                <input type="password" class="form-control form-control-lg" id="password" name="password" required
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 12px 16px; font-size: 16px; min-width: 100%;">
                                <button type="button" class="password-toggle-btn" onclick="togglePassword('password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">Remember me</label>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-lg text-white fw-bold"
                                    style="background: linear-gradient(135deg, #2c5530 0%, #34495e 100%);
                                           border: none; border-radius: 10px; padding: 12px;
                                           transition: all 0.3s ease;">
                                <i class="fas fa-sign-in-alt me-2"></i> Login
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4 text-center">
                        <p class="mb-0 text-muted">Don't have an account?
                            <a href="register.php" class="text-decoration-none fw-semibold"
                               style="color: #2c5530;">Register here</a>
                        </p>
                        <p class="mt-2">
                            <a href="forgot-password.php" class="text-decoration-none text-muted">
                                Forgot your password?
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Form JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth focus transitions
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'all 0.3s ease';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add button hover effects
    const btn = document.querySelector('.btn');
    if (btn) {
        btn.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(135deg, #1e3a8a 0%, #2c5530 100%)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.background = 'linear-gradient(135deg, #2c5530 0%, #34495e 100%)';
        });
    }
});

// Password toggle function
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.parentElement.querySelector('.password-toggle-btn');
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
        button.setAttribute('title', 'Hide password');
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
        button.setAttribute('title', 'Show password');
    }
    
    // Add animation
    button.style.transform = 'translateY(-50%) scale(1.1)';
    setTimeout(() => {
        button.style.transform = 'translateY(-50%) scale(1)';
    }, 150);
}
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?> 