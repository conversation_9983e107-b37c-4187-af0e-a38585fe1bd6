<?php
// Test Payment Processor - For testing payment flow without Flutterwave API
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in and is a buyer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    redirect('login.php');
}

$buyerId = $_SESSION['user_id'];
$buyerName = $_SESSION['username'];
$buyerEmail = $_SESSION['email'];

// Get parameters
$propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;

if (!$propertyId) {
    redirect('index.php');
}

// Get property details
$property = getPropertyById($propertyId);
if (!$property) {
    redirect('index.php');
}

$amount = $property['price'];
$sellerId = $property['seller_id'];
$sellerName = htmlspecialchars($property['seller_name']);
$propertyTitle = $property['title'];

// Process test payment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Generate test transaction ID
    $transactionId = 'TEST_TXN_' . time() . '_' . rand(1000, 9999);
    $now = date('Y-m-d H:i:s');
    
    try {
        // Start transaction
        $conn->begin_transaction();
        
        // Insert payment record
        $sql = "INSERT INTO payments (property_id, buyer_id, seller_id, amount, date, status, transaction_id, payment_method) VALUES (?, ?, ?, ?, ?, 'completed', ?, 'Test Payment')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiidss', $propertyId, $buyerId, $sellerId, $amount, $now, $transactionId);
        $stmt->execute();
        
        // Mark property as sold
        $sql = "UPDATE properties SET status = 'Sold' WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $propertyId);
        $stmt->execute();
        
        // Send messages to buyer and seller
        // To Buyer
        $buyerMsg = "Congratulations! You have successfully purchased the property titled \"$propertyTitle\". 

Payment Details:
- Property: $propertyTitle (ID: $propertyId)
- Amount Paid: TZS " . number_format($amount, 2) . "
- Transaction ID: $transactionId
- Payment Method: Test Payment
- Date: " . date('F j, Y \a\t g:i A') . "

Thank you for your payment. The property is now yours!

Best regards,
TX Properties Team";

        $buyerSubject = "Property Purchase Confirmation - $propertyTitle";
        $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiss', $sellerId, $buyerId, $propertyId, $buyerSubject, $buyerMsg);
        $stmt->execute();
        
        // To Seller
        $sellerMsg = "Great news! Your property titled \"$propertyTitle\" has been sold via Test Payment.

Sale Details:
- Property: $propertyTitle (ID: $propertyId)
- Buyer: $buyerName
- Sale Amount: TZS " . number_format($amount, 2) . "
- Transaction ID: $transactionId
- Payment Method: Test Payment
- Date: " . date('F j, Y \a\t g:i A') . "

The payment has been processed successfully and your property is now marked as SOLD.

Congratulations on your successful sale!

Best regards,
TX Properties Team";

        $sellerSubject = "Property Sold - $propertyTitle";
        $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiss', $buyerId, $sellerId, $propertyId, $sellerSubject, $sellerMsg);
        $stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        // Redirect to success page
        header('Location: payment_success.php?property_id=' . $propertyId . '&transaction_id=' . $transactionId . '&amount=' . $amount . '&property_title=' . urlencode($propertyTitle));
        exit;
        
    } catch (Exception $e) {
        // Rollback on error
        $conn->rollback();
        $error = "Payment processing failed: " . $e->getMessage();
    }
}

require_once 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg">
                <div class="card-header bg-warning text-dark">
                    <h3 class="mb-0"><i class="fas fa-flask me-2"></i>Test Payment Processor</h3>
                </div>
                <div class="card-body p-4">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>Test Payment Mode</h5>
                        <p class="mb-0">This is a test payment processor that simulates a successful payment without using the actual Flutterwave API. Use this to test the complete payment flow.</p>
                    </div>
                    
                    <h4 class="mb-3">Property Purchase Details</h4>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Property Information:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Property:</strong></td>
                                    <td><?php echo htmlspecialchars($propertyTitle); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Property ID:</strong></td>
                                    <td>#<?php echo $propertyId; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Current Status:</strong></td>
                                    <td><span class="badge bg-<?php echo $property['status'] === 'Available' ? 'success' : 'danger'; ?>"><?php echo $property['status']; ?></span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Payment Information:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Buyer:</strong></td>
                                    <td><?php echo htmlspecialchars($buyerName); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?php echo htmlspecialchars($buyerEmail); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Amount:</strong></td>
                                    <td class="text-success"><strong>TZS <?php echo number_format($amount, 2); ?></strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Test Payment Actions:</h6>
                        <ul class="mb-0">
                            <li>Property status will be changed to "Sold"</li>
                            <li>Property will be hidden from public listings</li>
                            <li>Messages will be sent to buyer and seller</li>
                            <li>Payment record will be created in database</li>
                            <li>You will be redirected to success page</li>
                        </ul>
                    </div>
                    
                    <form method="POST" class="text-center">
                        <button type="submit" class="btn btn-warning btn-lg px-5">
                            <i class="fas fa-flask me-2"></i>Process Test Payment
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Property
                        </a>
                        <a href="pay.php?property_id=<?php echo $propertyId; ?>" class="btn btn-primary">
                            <i class="fas fa-credit-card me-2"></i>Use Real Flutterwave
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
