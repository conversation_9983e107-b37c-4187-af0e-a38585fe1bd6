<?php
// Test validation functions
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Validation Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='assets/css/style.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔍 Validation System Test</h1>";

// Test data
$testData = [
    'name' => '<PERSON>\'Connor',
    'username' => 'john.doe123',
    'email' => '<EMAIL>',
    'phone' => '+255123456789',
    'property_title' => 'Beautiful 3-Bedroom House in Dar es Salaam',
    'price' => '150,000.00',
    'description' => 'This is a beautiful house with modern amenities and great location.',
    'password' => 'SecurePass123'
];

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'>✅ Validation Tests</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Test each validation function
echo "<h5>Name Validation:</h5>";
$nameResult = validateName($testData['name']);
echo "<p class='" . ($nameResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $nameResult['valid'] ? "✅ Valid" : "❌ " . $nameResult['message'];
echo "</p>";

echo "<h5>Username Validation:</h5>";
$usernameResult = validateUsername($testData['username']);
echo "<p class='" . ($usernameResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $usernameResult['valid'] ? "✅ Valid" : "❌ " . $usernameResult['message'];
echo "</p>";

echo "<h5>Email Validation:</h5>";
$emailResult = validateEmail($testData['email']);
echo "<p class='" . ($emailResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $emailResult['valid'] ? "✅ Valid" : "❌ " . $emailResult['message'];
echo "</p>";

echo "<h5>Phone Validation:</h5>";
$phoneResult = validatePhone($testData['phone']);
echo "<p class='" . ($phoneResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $phoneResult['valid'] ? "✅ Valid" : "❌ " . $phoneResult['message'];
echo "</p>";

echo "<h5>Property Title Validation:</h5>";
$titleResult = validatePropertyTitle($testData['property_title']);
echo "<p class='" . ($titleResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $titleResult['valid'] ? "✅ Valid" : "❌ " . $titleResult['message'];
echo "</p>";

echo "<h5>Price Validation:</h5>";
$priceResult = validatePropertyPrice($testData['price']);
echo "<p class='" . ($priceResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $priceResult['valid'] ? "✅ Valid (Cleaned: " . $priceResult['cleaned_price'] . ")" : "❌ " . $priceResult['message'];
echo "</p>";

echo "<h5>Description Validation:</h5>";
$descResult = validateDescription($testData['description']);
echo "<p class='" . ($descResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $descResult['valid'] ? "✅ Valid" : "❌ " . $descResult['message'];
echo "</p>";

echo "<h5>Password Validation:</h5>";
$passResult = validatePassword($testData['password']);
echo "<p class='" . ($passResult['valid'] ? 'text-success' : 'text-danger') . "'>";
echo $passResult['valid'] ? "✅ Valid" : "❌ " . $passResult['message'];
echo "</p>";

echo "</div></div></div>";

echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'>🧪 Test Data</h3>";
echo "</div>";
echo "<div class='card-body'>";

foreach ($testData as $field => $value) {
    echo "<p><strong>" . ucfirst(str_replace('_', ' ', $field)) . ":</strong><br>";
    echo "<code>" . htmlspecialchars($value) . "</code></p>";
}

echo "</div></div>";

// Test helper functions
echo "<div class='card mt-3'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h3 class='mb-0'>🔧 Helper Functions</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h5>Capitalize Words:</h5>";
echo "<p>Input: <code>john o'connor-smith</code></p>";
echo "<p>Output: <code>" . capitalizeWords("john o'connor-smith") . "</code></p>";

echo "<h5>Clean Phone Number:</h5>";
echo "<p>Input: <code>+255 (123) 456-789</code></p>";
echo "<p>Output: <code>" . cleanPhoneNumber("+255 (123) 456-789") . "</code></p>";

echo "<h5>Format Price (from functions.php):</h5>";
echo "<p>Sale: <code>" . formatPrice(150000, 'Sale') . "</code></p>";
echo "<p>Rent: <code>" . formatPrice(50000, 'Rent') . "</code></p>";

echo "</div></div>";
echo "</div>";

echo "</div>";

// Test batch validation
echo "<div class='card mt-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'>📋 Batch Validation Test</h3>";
echo "</div>";
echo "<div class='card-body'>";

$rules = [
    'name' => ['type' => 'name', 'min' => 2, 'max' => 100],
    'username' => ['type' => 'username', 'min' => 3, 'max' => 30],
    'email' => ['type' => 'email'],
    'phone' => ['type' => 'phone', 'required' => false],
    'property_title' => ['type' => 'property_title', 'min' => 5, 'max' => 200],
    'price' => ['type' => 'price', 'min' => 1000],
    'description' => ['type' => 'description', 'min' => 10, 'max' => 5000],
    'password' => ['type' => 'password', 'min' => 8]
];

$batchResult = validateFormData($testData, $rules);

echo "<h5>Batch Validation Result:</h5>";
if ($batchResult['valid']) {
    echo "<div class='alert alert-success'>";
    echo "<h6>✅ All validations passed!</h6>";
    echo "<p>Cleaned data ready for database storage.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h6>❌ Validation errors found:</h6>";
    foreach ($batchResult['errors'] as $field => $error) {
        echo "<p><strong>" . ucfirst($field) . ":</strong> " . $error . "</p>";
    }
    echo "</div>";
}

echo "</div></div>";

echo "<div class='text-center mt-4'>";
echo "<a href='register.php' class='btn btn-primary me-2'>Test Registration Form</a>";
echo "<a href='add_property.php' class='btn btn-success me-2'>Test Property Form</a>";
echo "<a href='contact.php' class='btn btn-info'>Test Contact Form</a>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
