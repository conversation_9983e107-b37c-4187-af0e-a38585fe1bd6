<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/rental_functions.php';
require_once 'includes/flutterwave_config.php';

// Check if user is logged in and is a buyer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    redirect('login.php');
}

$propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;

if (!$propertyId) {
    redirect('index.php');
}

// Get property details
$property = getPropertyById($propertyId);
if (!$property || $property['listing_type'] !== 'Rent') {
    redirect('index.php');
}

// Check if property is already rented
$currentRental = isPropertyRented($propertyId);

$tenantId = $_SESSION['user_id'];
$tenantName = $_SESSION['username'];
$tenantEmail = $_SESSION['email'];
$landlordId = $property['seller_id'];
$landlordName = $property['seller_name'];
$monthlyRate = $property['price'];
$propertyTitle = $property['title'];

// Get rental duration options
$rentalDurations = getRentalDurations();

require_once 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="text-primary">🏠 Rent Property</h1>
                <p class="text-muted">Complete your rental payment for this property</p>
            </div>

            <?php if ($currentRental): ?>
                <!-- Property Already Rented -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Property Currently Rented</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Rental Information:</h6>
                            <ul class="mb-0">
                                <li><strong>Current Tenant:</strong> <?php echo htmlspecialchars($currentRental['tenant_name']); ?></li>
                                <li><strong>Rental Period:</strong> <?php echo date('M j, Y', strtotime($currentRental['rental_start_date'])); ?> to <?php echo date('M j, Y', strtotime($currentRental['rental_end_date'])); ?></li>
                                <li><strong>Monthly Rate:</strong> TZS <?php echo number_format($currentRental['monthly_rate'], 2); ?></li>
                            </ul>
                        </div>
                        <p class="text-muted">This property is currently rented and not available for new rental agreements until the current lease expires.</p>
                        <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Property
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Rental Payment Form -->
                <div class="row">
                    <!-- Property Details -->
                    <div class="col-md-6">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-home me-2"></i>Property Details</h5>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title"><?php echo htmlspecialchars($propertyTitle); ?></h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Property ID:</strong></td>
                                        <td>#<?php echo $propertyId; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Location:</strong></td>
                                        <td><?php echo htmlspecialchars($property['location']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Property Type:</strong></td>
                                        <td><?php echo htmlspecialchars($property['property_type']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Bedrooms:</strong></td>
                                        <td><?php echo $property['bedrooms']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Bathrooms:</strong></td>
                                        <td><?php echo $property['bathrooms']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Monthly Rate:</strong></td>
                                        <td class="text-success"><strong>TZS <?php echo number_format($monthlyRate, 2); ?></strong></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Landlord Details -->
                        <div class="card shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-user me-2"></i>Landlord Information</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($landlordName); ?></p>
                                <p class="mb-0"><strong>Property Owner</strong></p>
                            </div>
                        </div>
                    </div>

                    <!-- Rental Payment Form -->
                    <div class="col-md-6">
                        <div class="card shadow-lg">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Rental Payment</h5>
                            </div>
                            <div class="card-body">
                                <form id="rentalForm">
                                    <!-- Tenant Information -->
                                    <div class="mb-3">
                                        <label class="form-label"><strong>Tenant Information:</strong></label>
                                        <div class="bg-light p-3 rounded">
                                            <p class="mb-1"><strong>Name:</strong> <?php echo htmlspecialchars($tenantName); ?></p>
                                            <p class="mb-0"><strong>Email:</strong> <?php echo htmlspecialchars($tenantEmail); ?></p>
                                        </div>
                                    </div>

                                    <!-- Rental Duration -->
                                    <div class="mb-3">
                                        <label for="rentDuration" class="form-label"><strong>Rental Duration:</strong></label>
                                        <select class="form-select" id="rentDuration" name="rent_duration" required>
                                            <option value="">Select rental duration</option>
                                            <?php foreach ($rentalDurations as $months => $label): ?>
                                                <option value="<?php echo $months; ?>" data-months="<?php echo $months; ?>">
                                                    <?php echo $label; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- Rental Start Date -->
                                    <div class="mb-3">
                                        <label for="startDate" class="form-label"><strong>Rental Start Date:</strong></label>
                                        <input type="date" class="form-control" id="startDate" name="start_date" 
                                               value="<?php echo date('Y-m-d'); ?>" min="<?php echo date('Y-m-d'); ?>" required>
                                    </div>

                                    <!-- Amount Calculation -->
                                    <div class="mb-4">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title">Payment Calculation:</h6>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted">Monthly Rate:</small>
                                                        <div><strong>TZS <?php echo number_format($monthlyRate, 2); ?></strong></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Duration:</small>
                                                        <div id="selectedDuration"><strong>-</strong></div>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted">Subtotal:</small>
                                                        <div id="subtotal"><strong>TZS 0.00</strong></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Discount:</small>
                                                        <div id="discount" class="text-success"><strong>TZS 0.00</strong></div>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="text-center">
                                                    <h5 class="text-success mb-0">Total Amount: <span id="totalAmount">TZS 0.00</span></h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Rental Period Display -->
                                    <div class="mb-4" id="rentalPeriod" style="display: none;">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-calendar me-2"></i>Rental Period:</h6>
                                            <p class="mb-0"><strong>From:</strong> <span id="periodStart">-</span> <strong>To:</strong> <span id="periodEnd">-</span></p>
                                        </div>
                                    </div>

                                    <!-- Payment Button -->
                                    <div class="d-grid">
                                        <button type="button" id="payRentBtn" class="btn btn-success btn-lg" disabled>
                                            <i class="fas fa-credit-card me-2"></i>Pay Rent - <span id="payButtonAmount">TZS 0.00</span>
                                        </button>
                                    </div>

                                    <div class="text-center mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-shield-alt me-1"></i>Secure payment powered by Flutterwave
                                        </small>
                                    </div>
                                </form>

                                <div class="text-center mt-3">
                                    <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Property
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="https://checkout.flutterwave.com/v3.js"></script>
<script>
const monthlyRate = <?php echo $monthlyRate; ?>;
const propertyId = <?php echo $propertyId; ?>;
const tenantId = <?php echo $tenantId; ?>;
const landlordId = <?php echo $landlordId; ?>;
const landlordName = "<?php echo addslashes($landlordName); ?>";

// Calculate rental amounts and dates
function calculateRental() {
    const durationSelect = document.getElementById('rentDuration');
    const startDateInput = document.getElementById('startDate');
    const selectedOption = durationSelect.options[durationSelect.selectedIndex];
    
    if (!selectedOption.value || !startDateInput.value) {
        resetCalculation();
        return;
    }
    
    const months = parseInt(selectedOption.value);
    const startDate = new Date(startDateInput.value);
    
    // Calculate amounts
    const subtotal = monthlyRate * months;
    let discount = 0;
    
    // Apply discounts
    if (months >= 12) {
        discount = subtotal * 0.05; // 5% discount for 1+ years
    } else if (months >= 6) {
        discount = subtotal * 0.03; // 3% discount for 6+ months
    }
    
    const total = subtotal - discount;
    
    // Calculate end date
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + months);
    
    // Update display
    document.getElementById('selectedDuration').innerHTML = `<strong>${selectedOption.text}</strong>`;
    document.getElementById('subtotal').innerHTML = `<strong>TZS ${subtotal.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>`;
    document.getElementById('discount').innerHTML = `<strong>-TZS ${discount.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>`;
    document.getElementById('totalAmount').textContent = `TZS ${total.toLocaleString('en-US', {minimumFractionDigits: 2})}`;
    document.getElementById('payButtonAmount').textContent = `TZS ${total.toLocaleString('en-US', {minimumFractionDigits: 2})}`;
    
    // Update rental period
    document.getElementById('periodStart').textContent = startDate.toLocaleDateString('en-US', {year: 'numeric', month: 'long', day: 'numeric'});
    document.getElementById('periodEnd').textContent = endDate.toLocaleDateString('en-US', {year: 'numeric', month: 'long', day: 'numeric'});
    document.getElementById('rentalPeriod').style.display = 'block';
    
    // Enable payment button
    document.getElementById('payRentBtn').disabled = false;

    return {
        months: months,
        subtotal: subtotal,
        discount: discount,
        total: total,
        startDate: startDateInput.value,
        endDate: endDate.toISOString().split('T')[0]
    };
}

function resetCalculation() {
    document.getElementById('selectedDuration').innerHTML = '<strong>-</strong>';
    document.getElementById('subtotal').innerHTML = '<strong>TZS 0.00</strong>';
    document.getElementById('discount').innerHTML = '<strong>TZS 0.00</strong>';
    document.getElementById('totalAmount').textContent = 'TZS 0.00';
    document.getElementById('payButtonAmount').textContent = 'TZS 0.00';
    document.getElementById('rentalPeriod').style.display = 'none';
    document.getElementById('payRentBtn').disabled = true;
}

// Event listeners
document.getElementById('rentDuration').addEventListener('change', calculateRental);
document.getElementById('startDate').addEventListener('change', calculateRental);

// Payment processing
document.getElementById('payRentBtn').onclick = function () {
    const calculation = calculateRental();
    
    if (!calculation) {
        alert('Please select rental duration and start date');
        return;
    }
    
    FlutterwaveCheckout({
        public_key: "<?php echo getFlutterwavePublicKey(); ?>",
        tx_ref: "rent_" + Date.now(),
        amount: calculation.total,
        currency: "TZS",
        payment_options: "card, mobilemoney",
        customer: {
            email: "<?php echo $tenantEmail; ?>",
            name: "<?php echo $tenantName; ?>"
        },
        callback: function (data) {
            console.log('Flutterwave rental payment callback:', data);
            
            const successStatuses = ['successful', 'completed', 'success'];
            const isSuccessful = successStatuses.includes(data.status?.toLowerCase());
            
            if (isSuccessful) {
                // Show loading message
                document.body.innerHTML = '<div class="container py-5 text-center"><div class="spinner-border text-primary" role="status"></div><h4 class="mt-3">Processing rental payment...</h4><p>Please wait while we process your rental agreement.</p></div>';
                
                // Redirect to rental payment processing
                window.location.href = "process_rental_payment.php?transaction_id=" + data.id +
                    "&property_id=" + propertyId +
                    "&tenant_id=" + tenantId +
                    "&landlord_id=" + landlordId +
                    "&monthly_rate=" + monthlyRate +
                    "&duration=" + calculation.months +
                    "&total_amount=" + calculation.total +
                    "&start_date=" + calculation.startDate +
                    "&landlord_name=" + encodeURIComponent(landlordName);
            } else {
                alert('Rental payment was not successful. Status: ' + (data.status || 'unknown'));
                console.error('Rental payment failed:', data);
            }
        },
        customizations: {
            title: "Rental Payment",
            description: "Rental payment for Property ID <?php echo $propertyId; ?>",
            logo: "assets/images/logo.png"
        }
    });
};


</script>

<?php require_once 'includes/footer.php'; ?>
