<?php
// Start output buffering
ob_start();

// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$userId = $_SESSION['user_id'];
$userRole = $_SESSION['user_role'];

// Fetch user details
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $userId);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        $username = sanitize($_POST['username']);
        $email = sanitize($_POST['email']);
        $phone = sanitize($_POST['phone']);
        $bio = isset($_POST['bio']) ? sanitize($_POST['bio']) : '';
        $errors = [];
        if (empty($username)) $errors[] = 'Username is required.';
        if (empty($email)) $errors[] = 'Email is required.';
        elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Invalid email address.';
        // Check for unique username/email
        $checkSql = "SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param('ssi', $username, $email, $userId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        if ($checkResult->num_rows > 0) $errors[] = 'Username or email already in use.';
        if (empty($errors)) {
            $updateSql = "UPDATE users SET username = ?, email = ?, phone = ?, bio = ? WHERE id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param('ssssi', $username, $email, $phone, $bio, $userId);
            if ($updateStmt->execute()) {
                $_SESSION['username'] = $username;
                $_SESSION['email'] = $email;
                $success = 'Profile updated successfully!';
                $stmt->execute();
                $result = $stmt->get_result();
                $user = $result->fetch_assoc();
            } else {
                $error = 'Error updating profile. Please try again.';
            }
        } else {
            $error = implode('<br>', $errors);
        }
    }
    if (isset($_POST['change_password'])) {
        $currentPassword = $_POST['current_password'];
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];
        $errors = [];
        if (empty($currentPassword)) $errors[] = 'Current password is required.';
        if (empty($newPassword)) $errors[] = 'New password is required.';
        elseif (strlen($newPassword) < 6) $errors[] = 'Password must be at least 6 characters.';
        if ($newPassword !== $confirmPassword) $errors[] = 'New passwords do not match.';
        if (empty($errors)) {
            if (password_verify($currentPassword, $user['password'])) {
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $updateSql = "UPDATE users SET password = ? WHERE id = ?";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->bind_param('si', $hashedPassword, $userId);
                if ($updateStmt->execute()) {
                    $success = 'Password changed successfully!';
                } else {
                    $error = 'Error changing password. Please try again.';
                }
            } else {
                $error = 'Current password is incorrect.';
            }
        } else {
            $error = implode('<br>', $errors);
        }
    }
}
?>

<div class='container my-4'>
    <h1>My Profile</h1>
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <div class="row">
        <div class="col-md-6">
            <form method="post" class="mb-4">
                <h4>Edit Profile</h4>
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                </div>
                <div class="mb-3">
                    <label for="phone" class="form-label">Phone</label>
                    <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone']); ?>">
                </div>
                <div class="mb-3">
                    <label for="bio" class="form-label">Bio</label>
                    <textarea class="form-control" id="bio" name="bio" rows="3"><?php echo htmlspecialchars($user['bio']); ?></textarea>
                </div>
                <button type="submit" name="update_profile" class="btn btn-primary">Save Changes</button>
            </form>
        </div>
        <div class="col-md-6">
            <form method="post" class="mb-4">
                <h4>Change Password</h4>
                <div class="mb-3">
                    <label for="current_password" class="form-label">Current Password</label>
                    <div class="password-field-wrapper">
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                        <button type="button" class="password-toggle-btn" onclick="togglePassword('current_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="new_password" class="form-label">New Password</label>
                    <div class="password-field-wrapper">
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <button type="button" class="password-toggle-btn" onclick="togglePassword('new_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                    <div class="password-field-wrapper">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        <button type="button" class="password-toggle-btn" onclick="togglePassword('confirm_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <button type="submit" name="change_password" class="btn btn-primary">Change Password</button>
            </form>
        </div>
    </div>
</div>

<script>
// Password toggle function
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.parentElement.querySelector('.password-toggle-btn');
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
        button.setAttribute('title', 'Hide password');
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
        button.setAttribute('title', 'Show password');
    }
    
    // Add animation
    button.style.transform = 'translateY(-50%) scale(1.1)';
    setTimeout(() => {
        button.style.transform = 'translateY(-50%) scale(1)';
    }, 150);
}
</script>

<?php
// Include footer
require_once 'includes/footer.php';

// End output buffering
ob_end_flush();
?>
