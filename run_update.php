<?php
// Include database configuration
require_once 'includes/config.php';

// SQL commands from update_database.sql
$sql = [
    // Add local_area column to properties table
    "ALTER TABLE properties ADD COLUMN local_area VARCHAR(100) AFTER location",

    // Add property features columns
    "ALTER TABLE properties ADD COLUMN has_electricity BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_gym BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_air_condition BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_security BOOLEAN DEFAULT FALSE",
    "ALTER TABLE properties ADD COLUMN has_pool BOOLEAN DEFAULT FALSE",

    // Update property_type ENUM to include new types
    "ALTER TABLE properties MODIFY COLUMN property_type ENUM('Land', 'House', 'Apartment', 'Commercial', 'Office', 'Office Space', 'Store/Shop')"
];

// Execute each SQL command
echo "<h1>Executing Database Updates</h1>";
foreach ($sql as $query) {
    echo "<p>Executing: " . htmlspecialchars($query) . "</p>";
    try {
        if ($conn->query($query)) {
            echo "<p style='color:green'>Success!</p>";
        } else {
            echo "<p style='color:red'>Error: " . $conn->error . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
    }
    echo "<hr>";
}

echo "<p><strong>All updates completed!</strong></p>";
?> 